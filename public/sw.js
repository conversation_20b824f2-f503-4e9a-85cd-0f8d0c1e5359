// Offline-first SW
const CACHE='gym-pwa-v2';
const SHELL=['/','/today','/macros','/history','/plan','/offline','/manifest.webmanifest','/icons/icon-192.png','/icons/icon-512.png'];
self.addEventListener('install',e=>e.waitUntil(caches.open(CACHE).then(c=>c.addAll(SHELL)).then(()=>self.skipWaiting())));
self.addEventListener('activate',e=>e.waitUntil(caches.keys().then(keys=>Promise.all(keys.map(k=>k===CACHE?undefined:caches.delete(k)))).then(()=>self.clients.claim())));
self.addEventListener('fetch',e=>{
  const r=e.request;
  if(r.mode==='navigate'){e.respondWith((async()=>{try{const f=await fetch(r);const c=await caches.open(CACHE);c.put(r,f.clone());return f;}catch(e){const c=await caches.open(CACHE);return (await c.match(r))||c.match('/offline');}})());return;}
  e.respondWith((async()=>{const c=await caches.open(CACHE);const m=await c.match(r);if(m) return m; try{const f=await fetch(r); c.put(r,f.clone()); return f;}catch(e){return m;}})());
});
