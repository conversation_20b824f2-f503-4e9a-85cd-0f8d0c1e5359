'use client';
import { useParams } from 'next/navigation';
import { useStore } from '@/lib/store-cloud';
import { MobilityCard } from '@/components/mobility-card';
import { FinisherCard } from '@/components/finisher-card';
import { ExerciseTransitionTimer } from '@/components/exercise-transition-timer';

export default function RecoveryPage() {
  const params = useParams<{ day: 'Wed'|'Sun' }>();
  const { plan } = useStore();
  const rd = plan.recovery?.find(r => r.day === params.day);
  if (!rd) return <div className="space-y-2"><h1 className="text-2xl font-bold">No recovery plan for {params.day}</h1></div>;

  return (
    <div className="space-y-3">
      <h1 className="text-2xl font-bold">{rd.title}</h1>
      <MobilityCard items={rd.mobility.items} />
      {rd.conditioning && <FinisherCard type={rd.conditioning.type} minutes={rd.conditioning.minutes} cue={rd.conditioning.cue} />}
      <div className="rounded-lg border p-3 dark:border-neutral-800">
        <h3 className="font-semibold">Block timer (optional)</h3>
        <ExerciseTransitionTimer seconds={300} />
      </div>
    </div>
  );
}
