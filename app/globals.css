@tailwind base;
@tailwind components;
@tailwind utilities;

:root { 
  --safe-tap: 44px; 
}

* {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

body { 
  @apply bg-white text-neutral-900 dark:bg-neutral-950 dark:text-neutral-100; 
}

button, a { 
  min-height: var(--safe-tap); 
  min-width: var(--safe-tap); 
}

.container { 
  @apply mx-auto px-3; 
  max-width: 720px; 
}

/* Dark mode color adjustments for better contrast */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

.dark {
  color-scheme: dark;
}
