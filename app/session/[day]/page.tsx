'use client';
import { useParams } from 'next/navigation';
import { useStore } from '@/lib/store-cloud';
import { ExerciseCard } from '@/components/exercise-card';
import { EXERCISES } from '@/lib/exercises';
import { useEffect, useMemo, useState } from 'react';
import type { SetLog } from '@/lib/types';
import { suggestNextLoad } from '@/lib/progression';
import { WarmupCard } from '@/components/warmup-card';
import { MobilityCard } from '@/components/mobility-card';
import { FinisherCard } from '@/components/finisher-card';
import { ExerciseTransitionTimer } from '@/components/exercise-transition-timer';

function computeRamps(workWeight: number | null, percents: number[]) {
  if (!workWeight) return [];
  const repsSeq = [5,3,1,1];
  return percents.slice(0, repsSeq.length).map((p, i) => ({ kg: Math.max(2.5, Math.round((workWeight * p)/2.5)*2.5), reps: repsSeq[i] || 1 }));
}

export default function SessionPage() {
  const params = useParams<{ day: any }>();
  const { plan, cfg, startSession, logSet, getLastSetsFor, getBestEst1RMFor } = useStore();
  const day = params.day;
  const dp = plan.microcycle.find(d => d.day === day);
  const [sessionId, setSessionId] = useState<string>('');
  const [lastSetsMap, setLastSetsMap] = useState<Record<string, SetLog[]>>({});
  const [bestMap, setBestMap] = useState<Record<string, number | null>>({});
  const [showMobility, setShowMobility] = useState(true);

  useEffect(() => { if (dp?.mobility?.optional) setShowMobility(false); }, [dp?.mobility?.optional]);
  useEffect(() => { if (dp) startSession(day).then(setSessionId); }, [day, dp, startSession]);
  useEffect(() => { if (!dp) return; (async () => {
      const map: Record<string, SetLog[]> = {}; const bmap: Record<string, number | null> = {};
      for (const block of dp.blocks) for (const p of block.prescriptions) { map[p.exerciseId] = await getLastSetsFor(p.exerciseId); bmap[p.exerciseId] = await getBestEst1RMFor(p.exerciseId); }
      setLastSetsMap(map); setBestMap(bmap);
    })(); }, [dp, getLastSetsFor, getBestEst1RMFor]);

  if (!dp) return <div className="space-y-2"><h1 className="text-2xl font-bold">No training session today</h1></div>;

  const nextLoads = useMemo(() => {
    const out: Record<string, number | null> = {};
    for (const block of dp.blocks) for (const p of block.prescriptions) {
      const ex = EXERCISES.find(e => e.id === p.exerciseId);
      out[p.exerciseId] = suggestNextLoad({
        prescription: p,
        lastSets: lastSetsMap[p.exerciseId],
        lastWeightKg: lastSetsMap[p.exerciseId]?.slice(-1)[0]?.weightKg,
        bestEst1RM: bestMap[p.exerciseId],
        cfg,
        isUpper: (ex?.muscleGroups || []).some(g => ['chest','triceps','shoulders','back','biceps'].includes(g))
      });
    }
    return out;
  }, [dp, lastSetsMap, bestMap, cfg]);

  const rampRows = useMemo(() => {
    const r: { exerciseId: string, items: {kg:number,reps:number}[] }[] = [];
    const ramps = dp.warmup?.ramps ?? [];
    for (const rr of ramps) {
      const work = nextLoads[rr.exerciseId] ?? null;
      const items = computeRamps(work, rr.percents);
      if (items.length) r.push({ exerciseId: rr.exerciseId, items });
    }
    return r;
  }, [dp.warmup?.ramps, nextLoads]);

  return (
    <div className="space-y-3">
      <h1 className="text-2xl font-bold">{dp.blocks[0]?.title || day}</h1>

      <div className="rounded-lg border p-3 dark:border-neutral-800">
        <h3 className="font-semibold mb-1">Between-exercise timer</h3>
        <ExerciseTransitionTimer seconds={120} />
      </div>

      {dp.warmup && (
        <div className="rounded-lg border p-3 dark:border-neutral-800">
          <WarmupCard cardio={dp.warmup.cardio} dynamic={dp.warmup.dynamic} />
          {rampRows.length > 0 && (
            <div className="text-sm mt-2">
              <b>Ramp-up sets:</b>
              <ul className="list-disc pl-5 mt-1">
                {rampRows.map(r => {
                  const ex = EXERCISES.find(e => e.id === r.exerciseId)?.name || r.exerciseId;
                  return <li key={r.exerciseId}>{ex}: {r.items.map(i=>`${i.reps} @ ${i.kg}kg`).join(', ')}</li>;
                })}
              </ul>
              <div className="text-xs opacity-70 mt-1">Ramp sets are non-fatiguing; reduce reps as load increases.</div>
            </div>
          )}
        </div>
      )}

      {dp.mobility && (
        <div className="rounded-lg border p-3 dark:border-neutral-800">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Mobility</h3>
            <label className="text-sm flex items-center gap-2"><span>Show today</span><input type="checkbox" checked={showMobility} onChange={e=>setShowMobility(e.target.checked)} /></label>
          </div>
          {showMobility && <div className="mt-2"><MobilityCard items={dp.mobility.items} /></div>}
        </div>
      )}

      {dp.blocks.map((b, i) => (
        <div key={i}>
          {b.title && <h2 className="text-lg font-semibold mt-2 mb-1">{b.title}</h2>}
          {b.prescriptions.map((p) => {
            const ex = EXERCISES.find(e => e.id === p.exerciseId);
            const ramps = dp.warmup?.ramps?.find(r=>r.exerciseId===p.exerciseId);
            return (
              <ExerciseCard
                key={p.exerciseId}
                name={ex?.name || p.exerciseId}
                exerciseId={p.exerciseId}
                sets={p.sets}
                repRange={p.repRange}
                rirTarget={p.rirTarget}
                tempo={p.tempo}
                restSec={p.restSec}
                cues={ex?.cues}
                subs={ex?.subs}
                nextLoad={nextLoads[p.exerciseId] ?? undefined}
                ramps={(ramps && nextLoads[p.exerciseId]) ? computeRamps(nextLoads[p.exerciseId]!, ramps.percents) : []}
                onLog={async (vals) => { await logSet(sessionId, { exerciseId: p.exerciseId, ...vals }); }}
              />
            );
          })}
        </div>
      ))}

      {dp.finisher && <FinisherCard type={dp.finisher.type} minutes={dp.finisher.minutes} cue={dp.finisher.cue} />}
    </div>
  );
}
