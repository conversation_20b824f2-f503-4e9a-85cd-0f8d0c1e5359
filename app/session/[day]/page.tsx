'use client';
import { useParams } from 'next/navigation';
import { useStore } from '@/lib/store-cloud';
import { ExerciseCard } from '@/components/exercise-card';
import { EXERCISES } from '@/lib/exercises';
import { useEffect, useMemo, useState } from 'react';
import type { SetLog } from '@/lib/types';
import { suggestNextLoad } from '@/lib/progression';
import { WarmupCard } from '@/components/warmup-card';
import { MobilityCard } from '@/components/mobility-card';
import { FinisherCard } from '@/components/finisher-card';
import { ExerciseTransitionTimer } from '@/components/exercise-transition-timer';

function computeRamps(workWeight: number | null, percents: number[]) {
  if (!workWeight) return [];
  const repsSeq = [5,3,1,1];
  return percents.slice(0, repsSeq.length).map((p, i) => ({ kg: Math.max(2.5, Math.round((workWeight * p)/2.5)*2.5), reps: repsSeq[i] || 1 }));
}

export default function SessionPage() {
  const params = useParams<{ day: any }>();
  const { plan, cfg, startSession, logSet, getLastSetsFor, getBestEst1RMFor, today } = useStore();
  const day = params.day;
  const dp = plan.microcycle.find(d => d.day === day);
  const [sessionId, setSessionId] = useState<string>('');
  const [currentSession, setCurrentSession] = useState<any>(null);
  const [lastSetsMap, setLastSetsMap] = useState<Record<string, SetLog[]>>({});
  const [bestMap, setBestMap] = useState<Record<string, number | null>>({});
  const [showMobility, setShowMobility] = useState(true);
  const [loading, setLoading] = useState(true);

  useEffect(() => { if (dp?.mobility?.optional) setShowMobility(false); }, [dp?.mobility?.optional]);

  // Initialize or find existing session
  useEffect(() => {
    if (!dp) return;

    const initializeSession = async () => {
      setLoading(true);
      try {
        const { dateISO } = today();

        // First, try to find an existing session for today
        console.log('🔍 Looking for existing session for', day, 'on', dateISO);
        const response = await fetch(`/api/sessions/history?limit=10`);

        if (response.ok) {
          const sessions = await response.json();
          const existingSession = sessions.find((s: any) =>
            s.day === day && s.dateISO === dateISO
          );

          if (existingSession) {
            console.log('✅ Found existing session:', existingSession);
            setSessionId(existingSession.id);
            setCurrentSession(existingSession);
          } else {
            console.log('🆕 Creating new session for', day);
            const newSessionId = await startSession(day);
            setSessionId(newSessionId);

            // Fetch the newly created session
            const newSessionResponse = await fetch(`/api/sessions/${newSessionId}`);
            if (newSessionResponse.ok) {
              const newSession = await newSessionResponse.json();
              setCurrentSession(newSession);
            }
          }
        }
      } catch (error) {
        console.error('❌ Error initializing session:', error);
        // Fallback to creating a new session
        try {
          const newSessionId = await startSession(day);
          setSessionId(newSessionId);
        } catch (fallbackError) {
          console.error('❌ Fallback session creation failed:', fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    initializeSession();
  }, [day, dp, startSession, today]);
  useEffect(() => { if (!dp) return; (async () => {
      const map: Record<string, SetLog[]> = {}; const bmap: Record<string, number | null> = {};
      for (const block of dp.blocks) for (const p of block.prescriptions) { map[p.exerciseId] = await getLastSetsFor(p.exerciseId); bmap[p.exerciseId] = await getBestEst1RMFor(p.exerciseId); }
      setLastSetsMap(map); setBestMap(bmap);
    })(); }, [dp, getLastSetsFor, getBestEst1RMFor]);

  if (!dp) return <div className="space-y-2"><h1 className="text-2xl font-bold">No training session today</h1></div>;

  if (loading) return (
    <div className="space-y-2">
      <h1 className="text-2xl font-bold">{dp.blocks[0]?.title || day}</h1>
      <div className="text-center py-8">Loading session...</div>
    </div>
  );

  const nextLoads = useMemo(() => {
    const out: Record<string, number | null> = {};
    for (const block of dp.blocks) for (const p of block.prescriptions) {
      const ex = EXERCISES.find(e => e.id === p.exerciseId);
      out[p.exerciseId] = suggestNextLoad({
        prescription: p,
        lastSets: lastSetsMap[p.exerciseId],
        lastWeightKg: lastSetsMap[p.exerciseId]?.slice(-1)[0]?.weightKg,
        bestEst1RM: bestMap[p.exerciseId],
        cfg,
        isUpper: (ex?.muscleGroups || []).some(g => ['chest','triceps','shoulders','back','biceps'].includes(g))
      });
    }
    return out;
  }, [dp, lastSetsMap, bestMap, cfg]);

  const rampRows = useMemo(() => {
    const r: { exerciseId: string, items: {kg:number,reps:number}[] }[] = [];
    const ramps = dp.warmup?.ramps ?? [];
    for (const rr of ramps) {
      const work = nextLoads[rr.exerciseId] ?? null;
      const items = computeRamps(work, rr.percents);
      if (items.length) r.push({ exerciseId: rr.exerciseId, items });
    }
    return r;
  }, [dp.warmup?.ramps, nextLoads]);

  return (
    <div className="space-y-3">
      <h1 className="text-2xl font-bold">{dp.blocks[0]?.title || day}</h1>

      {/* Debug info */}
      {currentSession && (
        <div className="text-xs text-gray-500 p-2 bg-gray-50 dark:bg-gray-800 rounded">
          <strong>Debug:</strong> Session ID: {sessionId} |
          Total sets logged: {currentSession.items?.length || 0} |
          Session date: {currentSession.dateISO}
        </div>
      )}

      <div className="rounded-lg border p-3 dark:border-neutral-800">
        <h3 className="font-semibold mb-1">Between-exercise timer</h3>
        <ExerciseTransitionTimer seconds={120} />
      </div>

      {dp.warmup && (
        <div className="rounded-lg border p-3 dark:border-neutral-800">
          <WarmupCard cardio={dp.warmup.cardio} dynamic={dp.warmup.dynamic} />
          {rampRows.length > 0 && (
            <div className="text-sm mt-2">
              <b>Ramp-up sets:</b>
              <ul className="list-disc pl-5 mt-1">
                {rampRows.map(r => {
                  const ex = EXERCISES.find(e => e.id === r.exerciseId)?.name || r.exerciseId;
                  return <li key={r.exerciseId}>{ex}: {r.items.map(i=>`${i.reps} @ ${i.kg}kg`).join(', ')}</li>;
                })}
              </ul>
              <div className="text-xs opacity-70 mt-1">Ramp sets are non-fatiguing; reduce reps as load increases.</div>
            </div>
          )}
        </div>
      )}

      {dp.mobility && (
        <div className="rounded-lg border p-3 dark:border-neutral-800">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Mobility</h3>
            <label className="text-sm flex items-center gap-2"><span>Show today</span><input type="checkbox" checked={showMobility} onChange={e=>setShowMobility(e.target.checked)} /></label>
          </div>
          {showMobility && <div className="mt-2"><MobilityCard items={dp.mobility.items} /></div>}
        </div>
      )}

      {dp.blocks.map((b, i) => (
        <div key={i}>
          {b.title && <h2 className="text-lg font-semibold mt-2 mb-1">{b.title}</h2>}
          {b.prescriptions.map((p) => {
            const ex = EXERCISES.find(e => e.id === p.exerciseId);
            const ramps = dp.warmup?.ramps?.find(r=>r.exerciseId===p.exerciseId);

            // Get existing sets for this exercise from current session
            const existingSets = currentSession?.items?.filter((item: any) =>
              item.exerciseId === p.exerciseId
            ).map((item: any, index: number) => ({
              weightKg: item.weightKg,
              reps: item.reps,
              rir: item.rir,
              setIndex: item.setIndex,
              completedAt: item.completedAt
            })) || [];

            return (
              <ExerciseCard
                key={p.exerciseId}
                name={ex?.name || p.exerciseId}
                exerciseId={p.exerciseId}
                sets={p.sets}
                repRange={p.repRange}
                rirTarget={p.rirTarget}
                tempo={p.tempo}
                restSec={p.restSec}
                cues={ex?.cues}
                subs={ex?.subs}
                nextLoad={nextLoads[p.exerciseId] ?? undefined}
                ramps={(ramps && nextLoads[p.exerciseId]) ? computeRamps(nextLoads[p.exerciseId]!, ramps.percents) : []}
                existingSets={existingSets}
                onLog={async (vals) => {
                  await logSet(sessionId, { exerciseId: p.exerciseId, ...vals });
                  // Refresh the current session to show the new set
                  try {
                    const response = await fetch(`/api/sessions/${sessionId}`);
                    if (response.ok) {
                      const updatedSession = await response.json();
                      setCurrentSession(updatedSession);
                    }
                  } catch (error) {
                    console.error('Error refreshing session:', error);
                  }
                }}
              />
            );
          })}
        </div>
      ))}

      {dp.finisher && <FinisherCard type={dp.finisher.type} minutes={dp.finisher.minutes} cue={dp.finisher.cue} />}
    </div>
  );
}
