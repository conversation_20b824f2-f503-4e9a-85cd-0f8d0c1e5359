'use client';
import Link from 'next/link';
import { useStore } from '@/lib/store-cloud';
import { MacroRing } from '@/components/macro-ring';
import { useEffect, useState, useCallback } from 'react';
import type { MacroEntry, Day } from '@/lib/types';
import { MobilityCard } from '@/components/mobility-card';
import { WarmupCard } from '@/components/warmup-card';
import { FinisherCard } from '@/components/finisher-card';
import { Card } from '@/components/ui';

export default function Today() {
  const { plan, today, macroPlan } = useStore();
  const [mounted, setMounted] = useState(false);
  const [entries, setEntries] = useState<MacroEntry[]>([]);
  const [isLoadingMacros, setIsLoadingMacros] = useState(false);
  const [currentDay, setCurrentDay] = useState<{ day: any, dateISO: string } | null>(null);

  // Ensure component only renders on client to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    setCurrentDay(today());
  }, [today]);

  // Fetch entries directly from API
  const fetchMacroEntries = useCallback(async (date: string) => {
    setIsLoadingMacros(true);
    console.log(`🔄 Today page: Starting fetch for ${date}`);
    try {
      const response = await fetch(`/api/macros?date=${date}`, {
        // Prevent caching issues
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        }
      });
      if (response.ok) {
        const data = await response.json();
        const entriesArray = Array.isArray(data) ? data : [];
        console.log(`📊 Today page fetched ${entriesArray.length} entries for ${date}:`, entriesArray);
        setEntries(entriesArray);
      } else {
        console.error('Today page: API response not ok:', response.status);
        setEntries([]);
      }
    } catch (error) {
      console.error('Today page: Error fetching macro entries:', error);
      setEntries([]);
    } finally {
      setIsLoadingMacros(false);
    }
  }, []);

  useEffect(() => {
    if (mounted && currentDay) {
      fetchMacroEntries(currentDay.dateISO);
    }
  }, [currentDay?.dateISO, fetchMacroEntries, mounted]);

  // Auto-refresh data every 30 seconds when page is visible
  useEffect(() => {
    if (!mounted) return;

    const interval = setInterval(() => {
      if (!document.hidden && currentDay) {
        fetchMacroEntries(currentDay.dateISO);
      }
    }, 30000);

    // Also refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden && currentDay) {
        fetchMacroEntries(currentDay.dateISO);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [mounted, currentDay?.dateISO, fetchMacroEntries]);

  if (!mounted || !currentDay) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Today</h1>
        <div className="text-center py-8">Loading...</div>
      </div>
    );
  }

  const t = currentDay;
  const dp = plan.microcycle.find(d => d.day === t.day);
  const rp = plan.recovery?.find(r => r.day === (t.day as any));
  const trainingDays: Day[] = ['Mon','Tue','Thu','Sat'] as any;
  const isTrainingDay = trainingDays.includes(t.day as any);
  const targets = {
    protein: macroPlan.protein_g,
    carbs: isTrainingDay ? (macroPlan.trainingDayVariant?.carbs_g ?? macroPlan.carbs_g) : macroPlan.carbs_g,
    fats: macroPlan.fats_g
  };
  
  // Ensure entries is always an array before calling reduce
  const safeEntries = Array.isArray(entries) ? entries : [];
  const totals = safeEntries.reduce((acc, e) => { 
    acc.protein += e.protein_g; 
    acc.carbs += e.carbs_g; 
    acc.fats += e.fats_g; 
    return acc; 
  }, { protein:0, carbs:0, fats:0 });

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Today ({t.day})</h1>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Macros</h2>
          <button 
            onClick={() => fetchMacroEntries(t.dateISO)} 
            disabled={isLoadingMacros}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 text-black rounded"
          >
            {isLoadingMacros ? 'Loading...' : 'Refresh'}
          </button>
        </div>
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center"><MacroRing target={targets.protein} current={totals.protein} label={`P\n${Math.max(0, targets.protein - totals.protein)}g`} /></div>
          <div className="text-center"><MacroRing target={targets.carbs} current={totals.carbs} label={`C\n${Math.max(0, targets.carbs - totals.carbs)}g`} /></div>
          <div className="text-center"><MacroRing target={targets.fats} current={totals.fats} label={`F\n${Math.max(0, targets.fats - totals.fats)}g`} /></div>
        </div>
        <div className="text-sm text-gray-600">
          {isLoadingMacros ? 'Loading entries...' : `${safeEntries.length} entries today`}
        </div>
      </div>

      {isTrainingDay && dp ? (
        <Card className="space-y-2">
          <div className="flex items-center justify-between">
            <div><h2 className="text-xl font-semibold">Session: {dp.blocks[0]?.title || 'Workout'}</h2><div className="text-sm opacity-80">Window 09:00–10:30</div></div>
            <Link href={`/session/${dp.day}`} className="rounded-md px-4 py-3 bg-neutral-900 text-white dark:bg-white dark:text-neutral-900">Start</Link>
          </div>
          {dp.warmup && <WarmupCard cardio={dp.warmup.cardio} dynamic={dp.warmup.dynamic} />}
          {dp.mobility && <MobilityCard items={dp.mobility.items} hint={dp.mobility.optional ? 'Optional' : 'Recommended'} />}
          {dp.finisher && <FinisherCard type={dp.finisher.type} minutes={dp.finisher.minutes} cue={dp.finisher.cue} />}
        </Card>
      ) : rp ? (
        <Card className="space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">{rp.title}</h2>
            <Link href={`/recovery/${rp.day}`} className="rounded-md px-4 py-3 bg-neutral-900 text-white dark:bg-white dark:text-neutral-900">Start</Link>
          </div>
          <MobilityCard items={rp.mobility.items} />
          {rp.conditioning && <FinisherCard type={rp.conditioning.type} minutes={rp.conditioning.minutes} cue={rp.conditioning.cue} />}
        </Card>
      ) : <Card><div className="text-sm">No plan found for today.</div></Card>}

      <Card>
        <h3 className="font-semibold mb-2">Carb timing</h3>
        <ul className="text-sm space-y-1">
          <li><b>Pre (08:00):</b> {macroPlan.timing.preWO.targetCarbs_g} g carbs</li>
          {macroPlan.timing.intra && <li><b>Intra:</b> {macroPlan.timing.intra.targetCarbs_g} g carbs (sugar+salt in water)</li>}
          <li><b>Post (≤{macroPlan.timing.postWO.windowMin} min):</b> {macroPlan.timing.postWO.targetCarbs_g} g carbs</li>
        </ul>
      </Card>
    </div>
  );
}
