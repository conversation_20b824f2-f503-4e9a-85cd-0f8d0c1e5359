'use client';
import { useState } from 'react';
import { useStore } from '@/lib/store-cloud';
import { Card, Button, Label } from '@/components/ui';
import { defaultPlan } from '@/lib/planDefaults';

export default function PlanPage() {
  const { plan, importPlan } = useStore();
  const [text, setText] = useState<string>('');
  const [warnings, setWarnings] = useState<string[]>([]);
  function onImport(){ const { warnings } = importPlan(text); setWarnings(warnings); }
  return (
    <div className="space-y-3">
      <h1 className="text-2xl font-bold">Plan</h1>
      <Card>
        <Label>Paste JSON plan</Label>
        <textarea className="w-full h-48 border rounded-md p-2" placeholder="Paste your plan JSON here..." value={text} onChange={e=>setText(e.target.value)} />
        <div className="flex gap-2 mt-2"><Button onClick={onImport}>Parse & Save</Button><Button onClick={()=>setText(JSON.stringify(defaultPlan, null, 2))}>Load default JSON</Button></div>
        {warnings.length>0 && <div className="text-sm text-amber-600 mt-2">{warnings.join(' ')}</div>}
      </Card>
      <Card>
        <h2 className="text-lg font-semibold mb-2">Current split</h2>
        <div className="text-sm opacity-80">{plan.split}</div>
        {plan.microcycle.map(d => (
          <div key={d.day} className="mt-3">
            <h3 className="font-semibold">{d.day}</h3>
            {d.warmup && <div className="text-xs opacity-80">Warm-up: {d.warmup.cardio?.mode} {d.warmup.cardio?.minutes}m; {d.warmup.dynamic?.length ?? 0} drills</div>}
            {d.blocks.map((b, i) => (
              <div key={i} className="ml-2">
                {b.title && <div className="text-sm opacity-80">{b.title}</div>}
                <ul className="list-disc pl-5 mt-1">
                  {b.prescriptions.map(p => <li key={p.exerciseId} className="text-sm">{p.exerciseId}: {p.sets}×{p.repRange[0]}–{p.repRange[1]} RIR {p.rirTarget ?? 2}</li>)}
                </ul>
              </div>
            ))}
            {d.finisher && <div className="text-xs opacity-80 mt-1">Finisher: {d.finisher.type} {d.finisher.minutes}m</div>}
          </div>
        ))}
      </Card>
    </div>
  );
}
