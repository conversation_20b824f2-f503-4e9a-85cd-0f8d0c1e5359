'use client';
import { useEffect, useState, useCallback } from 'react';
import { useStore } from '@/lib/store-cloud';
import { Input, Label, Button, Card, Select } from '@/components/ui';
import type { MacroEntry } from '@/lib/types';

export default function MacrosPage() {
  const { macroPlan, setMacroPlan, addMacroEntry } = useStore();
  const [mounted, setMounted] = useState(false);
  const [isTrainingDay, setIsTrainingDay] = useState(true);
  const [dateISO, setDateISO] = useState<string>('');
  const [entries, setEntries] = useState<MacroEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Ensure component only renders on client to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    setDateISO(new Date().toISOString().slice(0,10));
  }, []);

  // Fetch entries directly from API
  const fetchEntries = useCallback(async (date: string) => {
    if (!date) return;
    setIsLoading(true);
    console.log(`🔄 Macros page: Starting fetch for ${date}`);
    try {
      const response = await fetch(`/api/macros?date=${date}`, {
        // Prevent caching issues
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        }
      });
      if (response.ok) {
        const data = await response.json();
        const entriesArray = Array.isArray(data) ? data : [];
        console.log(`📊 Macros page fetched ${entriesArray.length} entries for ${date}:`, entriesArray);
        setEntries(entriesArray);
        console.log(`✅ Loaded ${entriesArray.length || 0} macro entries from database`);
      } else {
        console.error('Macros page: API response not ok:', response.status);
        setEntries([]);
      }
    } catch (error) {
      console.error('Macros page: Error fetching macro entries:', error);
      setEntries([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => { 
    if (mounted && dateISO) {
      fetchEntries(dateISO); 
    }
  }, [dateISO, fetchEntries, mounted]);

  if (!mounted) {
    return <div>Loading...</div>;
  }

  const targets = {
    protein: macroPlan.protein_g,
    carbs: isTrainingDay ? (macroPlan.trainingDayVariant?.carbs_g ?? macroPlan.carbs_g) : macroPlan.carbs_g,
    fats: macroPlan.fats_g
  };
  const totals = entries.reduce((acc, e) => { acc.protein += e.protein_g; acc.carbs += e.carbs_g; acc.fats += e.fats_g; return acc; }, {protein:0, carbs:0, fats:0});

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Macros</h1>
      <Card>
        <div className="grid grid-cols-3 gap-2">
          <div><Label>P target</Label><Input value={macroPlan.protein_g} onChange={e=>setMacroPlan({...macroPlan, protein_g: Number(e.target.value) || 0})} /></div>
          <div><Label>C target</Label><Input value={macroPlan.carbs_g} onChange={e=>setMacroPlan({...macroPlan, carbs_g: Number(e.target.value) || 0})} /></div>
          <div><Label>F target</Label><Input value={macroPlan.fats_g} onChange={e=>setMacroPlan({...macroPlan, fats_g: Number(e.target.value) || 0})} /></div>
          <div className="col-span-3"><Label>Training-day carbs</Label><Input value={macroPlan.trainingDayVariant?.carbs_g ?? 0} onChange={e=>setMacroPlan({...macroPlan, trainingDayVariant: { carbs_g: Number(e.target.value) || 0 }})} /></div>
        </div>
        <div className="mt-2 text-sm opacity-80">Timing (09:00–10:30): Pre {macroPlan.timing.preWO.targetCarbs_g}g, Intra {macroPlan.timing.intra?.targetCarbs_g ?? 0}g, Post {macroPlan.timing.postWO.targetCarbs_g}g.</div>
      </Card>

      <Card>
        <div className="grid grid-cols-2 gap-3 items-end">
          <div><Label>Date</Label><Input type="date" value={dateISO} onChange={e=>setDateISO(e.target.value)} /></div>
          <div>
            <Label>Day type</Label>
            <Select value={isTrainingDay ? 'train':'rest'} onChange={e=>setIsTrainingDay(e.target.value==='train')}>
              <option value="train">Training day</option>
              <option value="rest">Rest day</option>
            </Select>
          </div>
        </div>
        <MacroQuickAdd dateISO={dateISO} onAdd={async (e)=>{ 
          await addMacroEntry(e); 
          // Small delay to ensure database consistency
          await new Promise(resolve => setTimeout(resolve, 200));
          // Refresh entries directly from database
          await fetchEntries(dateISO);
        }} />
        <div className="mt-3 text-sm">
          {isLoading ? (
            <span className="text-gray-500">Loading entries...</span>
          ) : (
            <>Remaining — P: <b>{Math.max(0, targets.protein - totals.protein)}g</b> • C: <b>{Math.max(0, targets.carbs - totals.carbs)}g</b> • F: <b>{Math.max(0, targets.fats - totals.fats)}g</b></>
          )}
        </div>
        <div className="mt-3">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-semibold">Today&apos;s entries</h3>
            <button 
              onClick={() => fetchEntries(dateISO)} 
              className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded"
            >
              Refresh
            </button>
          </div>
          {isLoading ? (
            <div className="text-sm text-gray-500">Loading...</div>
          ) : entries.length === 0 ? (
            <div className="text-sm text-gray-500">No entries for this date</div>
          ) : (
            <div>
              <div className="text-xs text-gray-500 mb-1">Found {entries.length} entries</div>
              <ul className="space-y-1 text-sm">
                {entries.map((e, index) => (
                  <li key={e.id} className="flex justify-between">
                    <span>{e.time} ({e.tag}) #{index + 1}</span>
                    <span>P{e.protein_g} C{e.carbs_g} F{e.fats_g}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

function MacroQuickAdd({ dateISO, onAdd }:{ dateISO:string, onAdd:(e:any)=>void }){
  const [time, setTime] = useState<string>(new Date().toTimeString().slice(0,5));
  const [protein, setP] = useState<string>('0'); const [carbs, setC] = useState<string>('0'); const [fats, setF] = useState<string>('0');
  const [tag, setTag] = useState<'preWO'|'intra'|'postWO'|'meal'>('meal');
  const [isAdding, setIsAdding] = useState(false);

  const handleAdd = async () => {
    setIsAdding(true);
    try {
      await onAdd({ dateISO, time, protein_g: Number(protein)||0, carbs_g: Number(carbs)||0, fats_g: Number(fats)||0, tag });
      // Clear form after successful add
      setP('0');
      setC('0');
      setF('0');
      setTime(new Date().toTimeString().slice(0,5));
    } catch (error) {
      console.error('Error adding macro entry:', error);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <div className="mt-3 grid grid-cols-4 gap-2 items-end">
      <div><Label>Time</Label><Input value={time} onChange={e=>setTime(e.target.value)} /></div>
      <div><Label>P</Label><Input inputMode="numeric" value={protein} onChange={e=>setP(e.target.value)} /></div>
      <div><Label>C</Label><Input inputMode="numeric" value={carbs} onChange={e=>setC(e.target.value)} /></div>
      <div><Label>F</Label><Input inputMode="numeric" value={fats} onChange={e=>setF(e.target.value)} /></div>
      <div className="col-span-2"><Label>Tag</Label><Select value={tag} onChange={e=>setTag(e.target.value as any)}><option value="preWO">Pre-WO</option><option value="intra">Intra</option><option value="postWO">Post-WO</option><option value="meal">Meal</option></Select></div>
      <div className="col-span-2"><Button onClick={handleAdd} disabled={isAdding} className="w-full">{isAdding ? 'Adding...' : 'Add'}</Button></div>
    </div>
  );
}
