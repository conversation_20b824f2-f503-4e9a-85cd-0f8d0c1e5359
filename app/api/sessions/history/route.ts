import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

const DEMO_USER_EMAIL = '<EMAIL>';

async function ensureUser() {
  let user = await supabaseDbService.getUserByEmail(DEMO_USER_EMAIL);
  if (!user) {
    user = await supabaseDbService.createUser(DEMO_USER_EMAIL);
  }
  return user;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const exerciseId = searchParams.get('exerciseId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const user = await ensureUser();

    if (exerciseId) {
      // Get last sets for a specific exercise
      const sets = await supabaseDbService.getLastSetsForExercise(exerciseId, user.id, limit);
      return NextResponse.json(sets);
    } else {
      // Get session history with optional date filtering
      const sessions = await supabaseDbService.getSessionHistory(user.id, {
        startDate,
        endDate,
        limit
      });
      return NextResponse.json(sessions);
    }
  } catch (error) {
    console.error('Error fetching session history:', error);
    return NextResponse.json({ error: 'Failed to fetch session history' }, { status: 500 });
  }
}
