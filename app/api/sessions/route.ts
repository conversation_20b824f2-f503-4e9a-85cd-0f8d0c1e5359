import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

const DEMO_USER_EMAIL = '<EMAIL>';

async function ensureUser() {
  let user = await supabaseDbService.getUserByEmail(DEMO_USER_EMAIL);
  if (!user) {
    user = await supabaseDbService.createUser(DEMO_USER_EMAIL);
  }
  return user;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const user = await ensureUser();

    const sessionWithUser = { 
      ...body, 
      userId: user.id 
    };
    
    const sessionId = await supabaseDbService.addSessionLog(sessionWithUser);
    
    return NextResponse.json({ id: sessionId });
  } catch (error) {
    console.error('Error creating session:', error);
    return NextResponse.json({ error: 'Failed to create session' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('id');

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID required' }, { status: 400 });
    }

    const user = await ensureUser();
    const session = await supabaseDbService.getSessionLog(sessionId, user.id);
    
    return NextResponse.json(session);
  } catch (error) {
    console.error('Error fetching session:', error);
    return NextResponse.json({ error: 'Failed to fetch session' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('id');

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID required' }, { status: 400 });
    }

    const user = await ensureUser();
    const result = await supabaseDbService.updateSessionLog(sessionId, user.id, body);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating session:', error);
    return NextResponse.json({ error: 'Failed to update session' }, { status: 500 });
  }
}
