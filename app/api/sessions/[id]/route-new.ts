import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

const DEMO_USER_EMAIL = '<EMAIL>';

async function ensureUser() {
  let user = await supabaseDbService.getUserByEmail(DEMO_USER_EMAIL);
  if (!user) {
    user = await supabaseDbService.createUser(DEMO_USER_EMAIL);
  }
  return user;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const user = await ensureUser();
    const session = await supabaseDbService.getSessionLog(resolvedParams.id, user.id);
    
    return NextResponse.json(session);
  } catch (error) {
    console.error('Error fetching session:', error);
    return NextResponse.json({ error: 'Failed to fetch session' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const body = await request.json();
    const user = await ensureUser();
    
    const result = await supabaseDbService.updateSessionLog(resolvedParams.id, user.id, body);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating session:', error);
    return NextResponse.json({ error: 'Failed to update session' }, { status: 500 });
  }
}
