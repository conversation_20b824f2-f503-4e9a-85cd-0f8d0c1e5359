import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

const DEMO_USER_EMAIL = '<EMAIL>';

async function ensureUser() {
  let user = await supabaseDbService.getUserByEmail(DEMO_USER_EMAIL);
  if (!user) {
    user = await supabaseDbService.createUser(DEMO_USER_EMAIL);
  }
  return user;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const exerciseId = searchParams.get('exerciseId');

    if (!exerciseId) {
      return NextResponse.json({ error: 'Exercise ID required' }, { status: 400 });
    }

    const user = await ensureUser();
    const best1RM = await supabaseDbService.getBestEst1RMForExercise(exerciseId, user.id);
    
    return NextResponse.json({ best1RM });
  } catch (error) {
    console.error('Error fetching best 1RM:', error);
    return NextResponse.json({ error: 'Failed to fetch best 1RM' }, { status: 500 });
  }
}
