import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const date = url.searchParams.get('date') || '2025-08-25';
    
    // Check environment variables
    const envCheck = {
      hasSupabaseUrl: !!process.env.SUPABASE_URL,
      hasAnonKey: !!process.env.SUPABASE_ANON_KEY,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      supabaseUrlLength: process.env.SUPABASE_URL?.length || 0,
    };

    // Try to get a demo user
    let demoUser;
    try {
      demoUser = await supabaseDbService.getUserByEmail('<EMAIL>');
      if (!demoUser) {
        demoUser = await supabaseDbService.createUser('<EMAIL>');
      }
    } catch (userError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to get demo user',
        userError: userError instanceof Error ? userError.message : 'Unknown user error',
        environment: envCheck
      }, { status: 500 });
    }
    
    // Try to get entries for the date
    const entries = await supabaseDbService.getMacroEntriesByDate(date, demoUser.id);

    return NextResponse.json({
      success: true,
      environment: envCheck,
      demoUserId: demoUser.id,
      date,
      entriesCount: entries.length,
      entries: entries,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
