import { NextRequest, NextResponse } from 'next/server';
import { supabaseDbService } from '@/lib/supabase-db';

// For now, we'll use a simple user system. Later we can integrate with proper auth
const DEMO_USER_EMAIL = '<EMAIL>';

async function ensureUser() {
  let user = await supabaseDbService.getUserByEmail(DEMO_USER_EMAIL);
  if (!user) {
    user = await supabaseDbService.createUser(DEMO_USER_EMAIL);
  }
  return user;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    console.log(`📊 /api/macros GET request for date: ${date}`);

    if (!date) {
      return NextResponse.json({ error: 'Date parameter required' }, { status: 400 });
    }

    const user = await ensureUser();
    console.log(`👤 User ID: ${user.id}`);
    
    const entries = await supabaseDbService.getMacroEntriesByDate(date, user.id);
    console.log(`📋 Found ${entries.length} entries for ${date}:`, entries.map(e => ({ id: e.id, time: e.time, protein: e.protein_g })));
    
    return NextResponse.json(entries);
  } catch (error) {
    console.error('Error fetching macro entries:', error);
    return NextResponse.json({ error: 'Failed to fetch macro entries' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const user = await ensureUser();

    const entryWithUser = { 
      ...body, 
      userId: user.id, 
      id: crypto.randomUUID() 
    };
    
    const result = await supabaseDbService.addMacroEntry(entryWithUser);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error adding macro entry:', error);
    return NextResponse.json({ error: 'Failed to add macro entry' }, { status: 500 });
  }
}
