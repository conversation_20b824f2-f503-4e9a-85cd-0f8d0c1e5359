'use client';
import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import { EXERCISES } from '@/lib/exercises';
import { ProgressionChart } from '@/components/progression-chart';
import type { SessionLog, SetLog } from '@/lib/types';

type SessionWithSets = SessionLog & {
  totalSets: number;
  exercises: string[];
};

export default function SessionHistoryPage() {
  const [sessions, setSessions] = useState<SessionWithSets[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedExercise, setSelectedExercise] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');

  const fetchSessions = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (dateFilter) {
        const endDate = new Date(dateFilter);
        endDate.setDate(endDate.getDate() + 30); // Get 30 days from selected date
        params.append('startDate', dateFilter);
        params.append('endDate', endDate.toISOString().split('T')[0]);
      }
      params.append('limit', '50');

      const response = await fetch(`/api/sessions/history?${params}`);
      if (response.ok) {
        const data = await response.json();

        // Process sessions to add metadata
        const processedSessions: SessionWithSets[] = data.map((session: SessionLog) => {
          const items = session.items || [];
          const exercises = [...new Set(items.map((item: SetLog) => item.exerciseId))];

          return {
            ...session,
            totalSets: items.length,
            exercises: exercises.map(id => EXERCISES.find(ex => ex.id === id)?.name || id)
          };
        });

        setSessions(processedSessions);
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, [dateFilter]);

  const filteredSessions = selectedExercise
    ? sessions.filter(session =>
        (session.items || []).some((item: SetLog) => item.exerciseId === selectedExercise)
      )
    : sessions;

  const uniqueExercises = [...new Set(
    sessions.flatMap(session =>
      (session.items || []).map((item: SetLog) => item.exerciseId)
    )
  )];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Session History</h1>
        <Button
          onClick={fetchSessions}
          disabled={loading}
          className="text-xs px-3 py-1"
        >
          {loading ? 'Loading...' : 'Refresh'}
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <label className="block text-sm font-medium mb-1">Filter by Exercise</label>
          <select
            value={selectedExercise}
            onChange={(e) => setSelectedExercise(e.target.value)}
            className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-600"
          >
            <option value="">All Exercises</option>
            {uniqueExercises.map(exerciseId => {
              const exercise = EXERCISES.find(ex => ex.id === exerciseId);
              return (
                <option key={exerciseId} value={exerciseId}>
                  {exercise?.name || exerciseId}
                </option>
              );
            })}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Start Date</label>
          <input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-600"
          />
        </div>
      </div>

      {/* Progression Chart for Selected Exercise */}
      {selectedExercise && (
        <div className="mb-6">
          <ProgressionChart
            exerciseId={selectedExercise}
            exerciseName={EXERCISES.find(ex => ex.id === selectedExercise)?.name || selectedExercise}
          />
        </div>
      )}

      {/* Session List */}
      <div className="space-y-3">
        {loading ? (
          <div className="text-center py-8">Loading sessions...</div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No sessions found. Start working out to see your history!
          </div>
        ) : (
          filteredSessions.map((session) => (
            <SessionCard key={session.id} session={session} selectedExercise={selectedExercise} />
          ))
        )}
      </div>
    </div>
  );
}

function SessionCard({ session, selectedExercise }: { session: SessionWithSets; selectedExercise: string }) {
  const [expanded, setExpanded] = useState(false);

  const formatDate = (dateISO: string) => {
    return new Date(dateISO).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const filteredItems = selectedExercise
    ? (session.items || []).filter((item: SetLog) => item.exerciseId === selectedExercise)
    : (session.items || []);

  const exerciseGroups = filteredItems.reduce((groups: Record<string, SetLog[]>, item: SetLog) => {
    if (!groups[item.exerciseId]) {
      groups[item.exerciseId] = [];
    }
    groups[item.exerciseId].push(item);
    return groups;
  }, {});

  return (
    <Card className="p-4">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div>
          <h3 className="font-semibold">{formatDate(session.dateISO)} - {session.day}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {session.totalSets} sets • {session.exercises.join(', ')}
          </p>
        </div>
        <div className="text-2xl">
          {expanded ? '−' : '+'}
        </div>
      </div>

      {expanded && (
        <div className="mt-4 space-y-3">
          {Object.entries(exerciseGroups).map(([exerciseId, sets]) => {
            const exercise = EXERCISES.find(ex => ex.id === exerciseId);
            const exerciseName = exercise?.name || exerciseId;

            return (
              <div key={exerciseId} className="border-l-2 border-blue-500 pl-3">
                <h4 className="font-medium text-sm">{exerciseName}</h4>
                <div className="space-y-1 mt-1">
                  {sets.map((set, index) => (
                    <div key={index} className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded flex justify-between">
                      <span>Set {set.setIndex + 1}: {set.weightKg}kg × {set.reps} reps</span>
                      <div className="flex gap-2">
                        {set.rir !== undefined && <span>RIR {set.rir}</span>}
                        {set.est1RM && <span className="opacity-70">~{Math.round(set.est1RM)}kg 1RM</span>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}

          {session.notes && (
            <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
              <p className="text-sm"><strong>Notes:</strong> {session.notes}</p>
            </div>
          )}

          {session.perceivedRecovery && (
            <div className="mt-2">
              <p className="text-sm"><strong>Recovery:</strong> {session.perceivedRecovery}/10</p>
            </div>
          )}
        </div>
      )}
    </Card>
  );
}
