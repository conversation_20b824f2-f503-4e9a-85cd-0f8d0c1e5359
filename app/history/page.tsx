'use client';
import { useEffect, useState } from 'react';
import { db } from '@/lib/db';
import { Button } from '@/components/ui';

export default function HistoryPage() {
  const [href, setHref] = useState<string>('');
  useEffect(()=>()=>{ if(href) URL.revokeObjectURL(href); },[href]);
  async function onExport(){
    const sessions = await db.sessionLogs.toArray();
    const macros = await db.macroEntries.toArray();
    const blob = new Blob([JSON.stringify({ sessions, macros }, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob); setHref(url);
  }
  return (<div className="space-y-3"><h1 className="text-2xl font-bold">History & Export</h1><p className="text-sm opacity-80">Export your logs as JSON.</p><Button onClick={onExport}>Build export</Button>{href && <div className="mt-2"><a className="underline" download={"gym-export.json"} href={href}>Download export</a></div>}</div>);
}
