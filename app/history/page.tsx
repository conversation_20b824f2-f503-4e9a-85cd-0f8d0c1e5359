'use client';
import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import { EXERCISES } from '@/lib/exercises';
import { ProgressionChart } from '@/components/progression-chart';
import type { SessionLog, SetLog } from '@/lib/types';

type SessionWithSets = SessionLog & {
  totalSets: number;
  exercises: string[];
};

export default function SessionHistoryPage() {
  const [sessions, setSessions] = useState<SessionWithSets[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedExercise, setSelectedExercise] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [mounted, setMounted] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  const fetchSessions = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (dateFilter) {
        const endDate = new Date(dateFilter);
        endDate.setDate(endDate.getDate() + 30); // Get 30 days from selected date
        params.append('startDate', dateFilter);
        params.append('endDate', endDate.toISOString().split('T')[0]);
      }
      params.append('limit', '50');

      console.log('📡 Fetching sessions with params:', params.toString());
      const response = await fetch(`/api/sessions/history?${params}`, {
        // Add cache busting to ensure fresh data
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Received sessions:', data.length);
        console.log('📝 Sessions with sets:', data.filter((s: SessionLog) => s.items && s.items.length > 0).length);

        // Process sessions to add metadata
        const processedSessions: SessionWithSets[] = data.map((session: SessionLog) => {
          const items = session.items || [];
          const exercises = [...new Set(items.map((item: SetLog) => item.exerciseId))];

          return {
            ...session,
            totalSets: items.length,
            exercises: exercises.map(id => EXERCISES.find(ex => ex.id === id)?.name || id)
          };
        });

        setSessions(processedSessions);
        setLastRefresh(new Date());
        console.log('✅ Sessions updated in state');
      } else {
        console.error('❌ Failed to fetch sessions:', response.status);
      }
    } catch (error) {
      console.error('❌ Error fetching sessions:', error);
    } finally {
      setLoading(false);
    }
  }, [dateFilter]);

  useEffect(() => {
    if (mounted) {
      fetchSessions();
    }
  }, [dateFilter, mounted, fetchSessions]);

  // Auto-refresh when page becomes visible and periodically
  useEffect(() => {
    if (!mounted) return;

    // Refresh every 10 seconds when page is visible
    const interval = setInterval(() => {
      if (!document.hidden) {
        console.log('🔄 Auto-refreshing session history...');
        fetchSessions();
      }
    }, 10000);

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('🔄 Page became visible, refreshing session history...');
        fetchSessions();
      }
    };

    const handleFocus = () => {
      console.log('🔄 Page focused, refreshing session history...');
      fetchSessions();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Listen for session updates from other parts of the app
    const handleSessionUpdate = () => {
      console.log('🔄 Session updated event received, refreshing...');
      fetchSessions();
    };

    window.addEventListener('sessionUpdated', handleSessionUpdate);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('sessionUpdated', handleSessionUpdate);
    };
  }, [mounted, fetchSessions]);

  const filteredSessions = selectedExercise
    ? sessions.filter(session =>
        (session.items || []).some((item: SetLog) => item.exerciseId === selectedExercise)
      )
    : sessions;

  const uniqueExercises = [...new Set(
    sessions.flatMap(session =>
      (session.items || []).map((item: SetLog) => item.exerciseId)
    )
  )];

  if (!mounted) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Session History</h1>
        <div className="text-center py-8">Loading...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Session History</h1>
          {lastRefresh && (
            <p className="text-xs text-gray-500">
              Last updated: {lastRefresh.toLocaleTimeString()}
            </p>
          )}
        </div>
        <Button
          onClick={fetchSessions}
          disabled={loading}
          className="px-4 py-2"
        >
          {loading ? 'Loading...' : '🔄 Refresh'}
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <label className="block text-sm font-medium mb-1">Filter by Exercise</label>
          <select
            value={selectedExercise}
            onChange={(e) => setSelectedExercise(e.target.value)}
            className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-600"
          >
            <option value="">All Exercises</option>
            {uniqueExercises.map(exerciseId => {
              const exercise = EXERCISES.find(ex => ex.id === exerciseId);
              return (
                <option key={exerciseId} value={exerciseId}>
                  {exercise?.name || exerciseId}
                </option>
              );
            })}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Start Date</label>
          <input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-600"
          />
        </div>
      </div>

      {/* Progression Chart for Selected Exercise */}
      {selectedExercise && (
        <div className="mb-6">
          <ProgressionChart
            exerciseId={selectedExercise}
            exerciseName={EXERCISES.find(ex => ex.id === selectedExercise)?.name || selectedExercise}
          />
        </div>
      )}

      {/* Debug Info */}
      <div className="text-xs text-gray-500 p-2 bg-gray-50 dark:bg-gray-800 rounded">
        <strong>Debug:</strong> Total sessions: {sessions.length} |
        Sessions with sets: {sessions.filter(s => s.totalSets > 0).length} |
        Filtered sessions: {filteredSessions.length}
        {selectedExercise && ` | Selected exercise: ${EXERCISES.find(ex => ex.id === selectedExercise)?.name || selectedExercise}`}
      </div>

      {/* Session List */}
      <div className="space-y-3">
        {loading ? (
          <div className="text-center py-8">Loading sessions...</div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {sessions.length === 0
              ? "No sessions found. Start working out to see your history!"
              : selectedExercise
                ? `No sessions found with ${EXERCISES.find(ex => ex.id === selectedExercise)?.name || selectedExercise} exercises.`
                : "No sessions match your current filters."
            }
          </div>
        ) : (
          filteredSessions.map((session) => (
            <SessionCard key={session.id} session={session} selectedExercise={selectedExercise} />
          ))
        )}
      </div>
    </div>
  );
}

function SessionCard({ session, selectedExercise }: { session: SessionWithSets; selectedExercise: string }) {
  const [expanded, setExpanded] = useState(false);

  const formatDate = (dateISO: string) => {
    return new Date(dateISO).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const filteredItems = selectedExercise
    ? (session.items || []).filter((item: SetLog) => item.exerciseId === selectedExercise)
    : (session.items || []);

  const exerciseGroups = filteredItems.reduce((groups: Record<string, SetLog[]>, item: SetLog) => {
    if (!groups[item.exerciseId]) {
      groups[item.exerciseId] = [];
    }
    groups[item.exerciseId].push(item);
    return groups;
  }, {});

  return (
    <Card className="p-4">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div>
          <h3 className="font-semibold">{formatDate(session.dateISO)} - {session.day}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {session.totalSets} sets • {session.exercises.join(', ')}
          </p>
        </div>
        <div className="text-2xl">
          {expanded ? '−' : '+'}
        </div>
      </div>

      {expanded && (
        <div className="mt-4 space-y-3">
          {Object.entries(exerciseGroups).map(([exerciseId, sets]) => {
            const exercise = EXERCISES.find(ex => ex.id === exerciseId);
            const exerciseName = exercise?.name || exerciseId;

            return (
              <div key={exerciseId} className="border-l-2 border-blue-500 pl-3">
                <h4 className="font-medium text-sm">{exerciseName}</h4>
                <div className="space-y-1 mt-1">
                  {sets.map((set, index) => (
                    <div key={index} className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded flex justify-between">
                      <span>Set {set.setIndex + 1}: {set.weightKg}kg × {set.reps} reps</span>
                      <div className="flex gap-2">
                        {set.rir !== undefined && <span>RIR {set.rir}</span>}
                        {set.est1RM && <span className="opacity-70">~{Math.round(set.est1RM)}kg 1RM</span>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}

          {session.notes && (
            <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
              <p className="text-sm"><strong>Notes:</strong> {session.notes}</p>
            </div>
          )}

          {session.perceivedRecovery && (
            <div className="mt-2">
              <p className="text-sm"><strong>Recovery:</strong> {session.perceivedRecovery}/10</p>
            </div>
          )}
        </div>
      )}
    </Card>
  );
}
