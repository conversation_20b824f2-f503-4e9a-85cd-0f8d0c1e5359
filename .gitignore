# Next.js build output
.next/
out/

# Node modules
node_modules/

# Environment variables
.env.local
.env.*.local

# Local development and editor configurations
.DS_Store
.vscode/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp.*
.eslintcache

# Tailwind CSS specific
tailwind.config.js.bak # If you create backups of your config

# Other common ignores
coverage/
dist/
build/

/lib/generated/prisma
