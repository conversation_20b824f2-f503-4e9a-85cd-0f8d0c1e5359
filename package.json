{"name": "gym-pwa-v2", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "vitest run", "test:watch": "vitest", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.14.0", "@supabase/supabase-js": "^2.56.0", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dexie": "^4.0.8", "next": "15.1.5", "next-auth": "^4.24.11", "postcss": "^8.4.41", "prisma": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.4.10", "zod": "^3.23.8", "zustand": "^4.5.7"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "typescript": "^5.5.4", "vitest": "^2.0.5"}}