'use client';
import * as React from 'react'; 
import { Input, Button, Label, Range } from './ui';

export function SetLogger({ onSubmit }:{ onSubmit:(v:{weightKg:number,reps:number,rir:number})=>void }){
  const [w,setW]=React.useState(''),[r,setR]=React.useState(''),[rir,setRIR]=React.useState(2);
  return (<div className="grid grid-cols-3 gap-2 items-end">
    <div><Label>Weight (kg)</Label><Input inputMode="decimal" value={w} onChange={e=>setW(e.target.value)} placeholder="e.g. 60" /></div>
    <div><Label>Reps</Label><Input inputMode="numeric" value={r} onChange={e=>setR(e.target.value)} placeholder="e.g. 10" /></div>
    <div className="col-span-3"><Label>RIR: {rir}</Label><Range min={0} max={5} step={1} value={rir} onChange={e=>setRIR(parseInt(e.target.value))} /></div>
    <div className="col-span-3"><Button onClick={()=>{ const W=parseFloat(w||'0'), R=parseInt(r||'0'); if(!isNaN(W)&&!isNaN(R)) onSubmit({weightKg:W,reps:R,rir}); }} className="w-full">Log set</Button></div>
  </div>);
}
