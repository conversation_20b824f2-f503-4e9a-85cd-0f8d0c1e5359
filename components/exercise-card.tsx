'use client';
import * as React from 'react';
import { Card } from './ui';
import { RestTimer } from './rest-timer';
import { SetLogger } from './set-logger';

type LoggedSet = {
  weightKg: number;
  reps: number;
  rir: number;
  setIndex: number;
  completedAt: string;
};

type Props={
  name:string;
  exerciseId:string;
  sets:number;
  repRange:[number,number];
  rirTarget?:number;
  tempo?:string;
  restSec?:number;
  cues?:string[];
  subs?:{id:string,name:string}[];
  onLog:(s:{weightKg:number,reps:number,rir:number,setIndex:number})=>void;
  nextLoad?:number|null;
  ramps?:{kg:number,reps:number}[];
  existingSets?:LoggedSet[];
};

export function ExerciseCard(p:Props){
  const { name, sets, repRange, rirTarget, tempo, restSec=90, cues=[], subs=[], onLog, nextLoad, ramps=[], existingSets=[] } = p;
  const [loggedSets, setLoggedSets] = React.useState<LoggedSet[]>(existingSets);

  // Update logged sets when existingSets prop changes
  React.useEffect(() => {
    setLoggedSets(existingSets);
  }, [existingSets]);

  const handleLogSet = (setData: {weightKg:number,reps:number,rir:number}) => {
    const setIndex = loggedSets.length;
    const newSet: LoggedSet = {
      ...setData,
      setIndex,
      completedAt: new Date().toISOString()
    };

    setLoggedSets(prev => [...prev, newSet]);
    onLog({ ...setData, setIndex });
  };

  return (
    <Card className="mb-3">
      <div className="flex items-baseline justify-between">
        <h3 className="text-lg font-semibold">{name}</h3>
        {typeof nextLoad==='number'&&<div className="text-xs opacity-80">Next: ~{nextLoad} kg</div>}
      </div>

      <div className="text-sm opacity-80">
        Sets: {sets} • Reps: {repRange[0]}–{repRange[1]}
        {typeof rirTarget==='number'?` • RIR ${rirTarget}`:''}
        {tempo?` • Tempo ${tempo}`:''}
      </div>

      {ramps.length>0 && (
        <div className="text-xs mt-1">
          Ramp-up: {ramps.map(r=>`${r.reps}@${r.kg}kg`).join(', ')} (non-fatiguing)
        </div>
      )}

      {cues.length>0 && (
        <div className="text-xs mt-1">Cues: {cues.join(' • ')}</div>
      )}

      {subs.length>0 && (
        <details className="text-xs mt-1">
          <summary className="cursor-pointer">Substitutions</summary>
          <ul className="list-disc pl-5 mt-1">
            {subs.map(s=><li key={s.id}>{s.name}</li>)}
          </ul>
        </details>
      )}

      {/* Display logged sets */}
      {loggedSets.length > 0 && (
        <div className="mt-2 space-y-1">
          <div className="text-sm font-medium">Logged Sets:</div>
          {loggedSets.map((set, index) => (
            <div key={index} className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded flex justify-between">
              <span>Set {index + 1}: {set.weightKg}kg × {set.reps} reps</span>
              <span className="opacity-70">RIR {set.rir}</span>
            </div>
          ))}
        </div>
      )}

      {/* Set logger - only show if not all sets completed */}
      {loggedSets.length < sets && (
        <div className="mt-2">
          <SetLogger onSubmit={handleLogSet} />
        </div>
      )}

      <div className="mt-2 flex items-center justify-between">
        <div className="text-sm">
          Completed: {loggedSets.length}/{sets}
          {loggedSets.length >= sets && <span className="text-green-600 ml-2">✓ Complete</span>}
        </div>
        <RestTimer seconds={restSec} />
      </div>
    </Card>
  );
}
