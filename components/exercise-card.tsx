'use client';
import * as React from 'react';
import { Card } from './ui';
import { RestTimer } from './rest-timer';
import { SetLogger } from './set-logger';

type Props={ name:string; exerciseId:string; sets:number; repRange:[number,number]; rirTarget?:number; tempo?:string; restSec?:number; cues?:string[]; subs?:{id:string,name:string}[]; onLog:(s:{weightKg:number,reps:number,rir:number})=>void; nextLoad?:number|null; ramps?:{kg:number,reps:number}[]; };

export function ExerciseCard(p:Props){
  const { name, sets, repRange, rirTarget, tempo, restSec=90, cues=[], subs=[], onLog, nextLoad, ramps=[] } = p;
  const [completed,setCompleted]=React.useState(0);
  return (<Card className="mb-3">
    <div className="flex items-baseline justify-between"><h3 className="text-lg font-semibold">{name}</h3>{typeof nextLoad==='number'&&<div className="text-xs opacity-80">Next: ~{nextLoad} kg</div>}</div>
    <div className="text-sm opacity-80">Sets: {sets} • Reps: {repRange[0]}–{repRange[1]} {typeof rirTarget==='number'?`• RIR ${rirTarget}`:''} {tempo?`• Tempo ${tempo}`:''}</div>
    {ramps.length>0 && <div className="text-xs mt-1">Ramp-up: {ramps.map(r=>`${r.reps}@${r.kg}kg`).join(', ')} (non-fatiguing)</div>}
    {cues.length>0 && <div className="text-xs mt-1">Cues: {cues.join(' • ')}</div>}
    {subs.length>0 && <details className="text-xs mt-1"><summary className="cursor-pointer">Substitutions</summary><ul className="list-disc pl-5 mt-1">{subs.map(s=><li key={s.id}>{s.name}</li>)}</ul></details>}
    <div className="mt-2"><SetLogger onSubmit={(v)=>{ onLog(v); setCompleted(c=>Math.min(sets,c+1)); }} /></div>
    <div className="mt-2 flex items-center justify-between"><div className="text-sm">Completed: {completed}/{sets}</div><RestTimer seconds={restSec} /></div>
  </Card>);
}
