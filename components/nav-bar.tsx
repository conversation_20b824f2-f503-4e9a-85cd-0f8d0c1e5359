'use client';
import Link from 'next/link'; 
import { usePathname } from 'next/navigation'; 
import { clsx } from 'clsx';
import { ThemeToggle } from './theme-toggle';

const tabs=[{href:'/today',label:'Today'},{href:'/macros',label:'Macros'},{href:'/history',label:'History'},{href:'/plan',label:'Plan'}];

export function NavBar(){ 
  const p=usePathname(); 
  
  return (
    <nav className="fixed bottom-0 inset-x-0 border-t bg-white/90 backdrop-blur dark:bg-neutral-900/90 dark:border-neutral-800">
      <div className="container">
        <div className="flex flex-col gap-2 py-2">
          <div className="flex items-center justify-end">
            <ThemeToggle />
          </div>
          <div className="grid grid-cols-4 gap-1">
            {tabs.map(t=>
              <Link 
                key={t.href} 
                href={t.href} 
                className={clsx(
                  'text-center rounded-md px-2 py-2 text-sm', 
                  p.startsWith(t.href)
                    ?'font-semibold bg-neutral-100 dark:bg-neutral-800'
                    :'opacity-80 hover:opacity-100'
                )}
              >
                {t.label}
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}