'use client';
import * as React from 'react'; import { Button } from './ui';
export function ExerciseTransitionTimer({ seconds=120 }:{seconds?:number}){
  const [r,setR]=React.useState(seconds),[on,setOn]=React.useState(false);
  React.useEffect(()=>{ if(!on) return; setR(seconds); const s=Date.now(); const id=setInterval(()=>{const left=Math.max(0,seconds-Math.floor((Date.now()-s)/1000)); setR(left); if(left<=0){clearInterval(id); setOn(false); try{(navigator as any).vibrate?.([100,80,100]);}catch{}}},200); return ()=>clearInterval(id);},[on,seconds]);
  return <div className="flex items-center gap-2"><div className="text-lg tabular-nums min-w-[64px]">{Math.floor(r/60)}:{String(r%60).padStart(2,'0')}</div><Button onClick={()=>setOn(o=>!o)}>{on?'Pause':'Start between-exercise'}</Button><Button onClick={()=>{setR(seconds); setOn(false);}}>Reset</Button></div>;
}
