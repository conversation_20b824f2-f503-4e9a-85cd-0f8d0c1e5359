import * as React from 'react';
import { clsx } from 'clsx';

export function Button(props: React.ButtonHTMLAttributes<HTMLButtonElement>){ 
  return <button 
    {...props} 
    className={clsx(
      'inline-flex items-center justify-center rounded-md border px-4 py-3 text-sm font-medium shadow-sm transition-colors',
      'bg-neutral-900 text-white border-neutral-900 hover:bg-neutral-800 hover:border-neutral-800',
      'dark:bg-white dark:text-neutral-900 dark:border-white dark:hover:bg-neutral-100 dark:hover:border-neutral-100',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      props.className
    )} 
  />; 
}

export function Card(props: React.HTMLAttributes<HTMLDivElement>){ 
  return <div 
    {...props} 
    className={clsx(
      'rounded-lg border p-3 shadow-sm transition-colors',
      'bg-white border-neutral-200',
      'dark:bg-neutral-900 dark:border-neutral-800',
      props.className
    )} 
  />; 
}

export function Input(props: React.InputHTMLAttributes<HTMLInputElement>){ 
  return <input 
    {...props} 
    className={clsx(
      'w-full rounded-md border px-3 py-2 text-sm transition-colors',
      'bg-white border-neutral-300 focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-transparent',
      'dark:bg-neutral-800 dark:border-neutral-600 dark:text-white dark:focus:ring-neutral-400',
      props.className
    )} 
  />; 
}

export function Select(props: React.SelectHTMLAttributes<HTMLSelectElement>){ 
  return <select 
    {...props} 
    className={clsx(
      'w-full rounded-md border px-3 py-2 text-sm transition-colors',
      'bg-white border-neutral-300 focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-transparent',
      'dark:bg-neutral-800 dark:border-neutral-600 dark:text-white dark:focus:ring-neutral-400',
      props.className
    )} 
  />; 
}

export function Label(props: React.LabelHTMLAttributes<HTMLLabelElement>){ 
  return <label 
    {...props} 
    className={clsx(
      'block text-sm font-medium mb-1',
      'text-neutral-700 dark:text-neutral-300',
      props.className
    )} 
  />; 
}

export function Range(props: React.InputHTMLAttributes<HTMLInputElement>){ 
  return <input 
    {...props}
    type="range"
    className={clsx(
      'w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer dark:bg-neutral-700',
      '[&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-neutral-600 [&::-webkit-slider-thumb]:cursor-pointer dark:[&::-webkit-slider-thumb]:bg-neutral-300',
      '[&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-neutral-600 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-none dark:[&::-moz-range-thumb]:bg-neutral-300',
      props.className
    )} 
  />; 
}
