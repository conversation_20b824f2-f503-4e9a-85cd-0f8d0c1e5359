'use client';
import * as React from 'react'; import { Card } from './ui';
type Item={ name:string; seconds:number; side?:boolean };
export function MobilityCard({ items, title='Mobility (10 min)', hint }:{ items:Item[]; title?:string; hint?:string }){
  return (<Card className="mb-3"><h3 className="text-lg font-semibold">{title}</h3>{hint && <div className="text-xs opacity-70">{hint}</div>}
    <ul className="list-disc pl-5 mt-1 text-sm">{items.map((m,i)=><li key={i}>{m.name} — {m.seconds}s{m.side?' / side':''}</li>)}</ul></Card>);
}
