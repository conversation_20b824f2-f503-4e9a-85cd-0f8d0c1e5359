'use client';
import * as React from 'react'; import { Card } from './ui';
export function FinisherCard({ type, minutes, cue }:{ type:'z2'|'sprints'|'walk', minutes:number, cue?:string }){
  const label= type==='z2'?'Zone 2': type==='sprints'?'Sprints':'Walk';
  return (<Card className="mb-3"><h3 className="text-lg font-semibold">Finisher</h3><div className="text-sm">{label}: {minutes} min{cue?` • ${cue}`:''}</div></Card>);
}
