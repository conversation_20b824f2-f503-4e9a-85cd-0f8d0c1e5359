'use client';
type Props={ target:number, current:number, label:string };
export function MacroRing({target,current,label}:Props){ 
  const pct=Math.min(1,current/Math.max(1,target)); 
  const r=28,c=2*Math.PI*r,d=c*pct; 
  return (
    <svg width="80" height="80" viewBox="0 0 80 80" className="text-neutral-600 dark:text-neutral-400">
      <circle cx="40" cy="40" r={r} stroke="currentColor" strokeWidth="8" fill="none" className="opacity-20"/>
      <circle cx="40" cy="40" r={r} stroke="currentColor" strokeWidth="8" fill="none" strokeDasharray={`${d} ${c-d}`} strokeLinecap="round" transform="rotate(-90 40 40)" className="opacity-100"/>
      <text x="40" y="40" textAnchor="middle" dominantBaseline="middle" className="fill-current text-[10px] font-medium">
        {label}
      </text>
    </svg>
  ); 
}
