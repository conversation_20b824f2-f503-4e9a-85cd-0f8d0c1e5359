'use client';
import * as React from 'react'; import { Card } from './ui';
type Dyn={ name:string; sets?:number; reps?:string; seconds?:number; };
export function WarmupCard({ cardio, dynamic }:{ cardio?:{mode:'treadmill'|'bike'|'stairs'|'walk'; minutes:number; cue?:string}, dynamic?:Dyn[] }){
  return (<Card className="mb-3"><h3 className="text-lg font-semibold">Warm-up</h3>
    {cardio && <div className="text-sm mt-1"><b>Cardio:</b> {cardio.mode} • {cardio.minutes} min{cardio.cue?` • ${cardio.cue}`:''}</div>}
    {dynamic && dynamic.length>0 && (<div className="text-sm mt-2"><b>Dynamic prep:</b><ul className="list-disc pl-5 mt-1">{dynamic.map((d,i)=><li key={i}>{d.name}{d.sets?` — ${d.sets} sets`:''}{d.reps?` × ${d.reps}`:''}{d.seconds?` • ${d.seconds}s`:''}</li>)}</ul></div>)}
  </Card>);
}
