# Gym App - Cloud Database Migration

This document explains how to migrate your gym app from browser-only storage to cloud-based storage for cross-device synchronization.

## What Changed

- **Before**: Data stored only in browser's IndexedDB (local only)
- **After**: Data synchronized to PostgreSQL cloud database (accessible from any device)

## Setup Instructions

### 1. Database Setup

You have several options for the database:

#### Option A: Prisma Postgres (Recommended)
1. Run `npx prisma login` to authenticate
2. Run `npx prisma postgres create gym-app` to create a new database
3. Copy the connection string to your `.env` file

#### Option B: Other PostgreSQL Provider
- Use any PostgreSQL provider (Supabase, Railway, Neon, etc.)
- Create a new database and get the connection string

### 2. Environment Variables

Create a `.env.local` file with:

```env
# Database
DATABASE_URL="your-postgresql-connection-string"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here" # Generate with: openssl rand -base64 32

# Google OAuth (for authentication)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 3. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret to your `.env.local`

### 4. Database Migration

```bash
# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Start the app
npm run dev
```

## How It Works

### Authentication
- Users sign in with Google
- Each user gets their own data space
- No data mixing between users

### Data Synchronization
- **Online**: Data automatically syncs to cloud database
- **Offline**: Data stored locally, syncs when back online
- **Migration**: Existing local data can be migrated to cloud

### Offline Support
- App continues to work offline
- Uses local storage as fallback
- Automatically syncs when connection restored

## Migration Process

When you first sign in after the update:

1. **Sign in** with Google account
2. **Migrate existing data** (if any) from local storage to cloud
3. **Verify data** appears correctly
4. **Clear local data** to avoid conflicts (optional)

## Benefits

✅ **Cross-device access** - Use from phone, laptop, tablet  
✅ **Data backup** - Never lose your workout history  
✅ **Offline support** - Still works without internet  
✅ **Automatic sync** - Data stays consistent everywhere  

## Troubleshooting

### Common Issues

**Build fails with TypeScript errors:**
```bash
npm run db:generate
```

**Database connection fails:**
- Check your `DATABASE_URL` in `.env.local`
- Ensure database is running and accessible

**Authentication not working:**
- Verify Google OAuth credentials
- Check redirect URI configuration
- Ensure `NEXTAUTH_SECRET` is set

**Data not syncing:**
- Check browser console for errors
- Verify you're signed in
- Check internet connection

## Development

```bash
# View database in browser
npm run db:studio

# Reset database (development only)
npx prisma migrate reset

# Deploy new database changes
npm run db:migrate
```

## Security Notes

- All data is scoped to authenticated users
- Google OAuth handles authentication
- Database credentials should never be committed to code
- Use strong `NEXTAUTH_SECRET` in production
