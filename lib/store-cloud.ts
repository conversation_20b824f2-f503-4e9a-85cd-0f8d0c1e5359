'use client';
import { create } from 'zustand';
import { defaultPlan } from './planDefaults';
import type { Day, MacroEntry, MacroPlan, Plan, ProgressionConfig, SessionLog, SetLog } from './types';
import { MacroPlan as MacroPlanSchema, ProgressionConfig as ProgressionConfigSchema } from './types';
import { epley1RM, suggestNextLoad } from './progression';
import { parsePlanText } from './parser';

// Clear all local storage on app start to force cloud-only behavior
if (typeof window !== 'undefined') {
  try {
    // Clear browser's localStorage and indexedDB
    localStorage.clear();
    indexedDB.deleteDatabase('GymDB');
    console.log('🧹 Cleared all local storage - using cloud only');
  } catch (error) {
    console.warn('Could not clear local storage:', error);
  }
}

type AppState = {
  plan: Plan;
  macroPlan: MacroPlan;
  cfg: ProgressionConfig;
  today: () => { day: Day, dateISO: string };
  startSession: (day: Day) => Promise<string>;
  logSet: (sessionId: string, set: Omit<SetLog, 'est1RM'|'completedAt'>) => Promise<void>;
  getLastSetsFor: (exerciseId: string) => Promise<SetLog[]>;
  getBestEst1RMFor: (exerciseId: string) => Promise<number | null>;
  importPlan: (text: string) => { warnings: string[] };
  listMacroEntriesByDate: (isoDate: string) => Promise<MacroEntry[]>;
  addMacroEntry: (entry: Omit<MacroEntry,'id'>) => Promise<void>;
  setMacroPlan: (mp: MacroPlan) => void;
  setCfg: (cfg: ProgressionConfig) => void;
};

const initialMacroPlan: MacroPlan = MacroPlanSchema.parse({
  id: 'default',
  protein_g: 150, carbs_g: 300, fats_g: 89,
  trainingDayVariant: { carbs_g: 380 },
  timing: { preWO: { start: '08:00', end: '09:00', targetCarbs_g: 70 }, intra: { targetCarbs_g: 20 }, postWO: { windowMin: 120, targetCarbs_g: 70 } }
});
const initialCfg: ProgressionConfig = ProgressionConfigSchema.parse({ upperIncKg:2.5, lowerIncKg:5, tmPercent:0.88, deloadLoadPct:0.9, deloadVolumePct:0.65, dbIncrementKg:2.5 });

export const useStore = create<AppState>()((set, get) => ({
  plan: defaultPlan,
  macroPlan: initialMacroPlan,
  cfg: initialCfg,

  today: () => {
    const now = new Date();
    const d = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'][now.getDay()] as Day;
    const dateISO = new Date(now.getTime() - now.getTimezoneOffset()*60000).toISOString().slice(0,10);
    return { day: d, dateISO };
  },

  startSession: async (day) => {
    const id = cryptoRandomId();
    const { dateISO } = get().today();
    const log: SessionLog = { id, dateISO, day, items: [] };
    
    try {
      // Cloud API only - no local fallback
      const response = await fetch('/api/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(log)
      });
      
      if (response.ok) {
        console.log('✅ Session started in cloud database');
        return id;
      } else {
        console.error('Failed to start session in cloud, status:', response.status);
        throw new Error(`API failed with status ${response.status}`);
      }
    } catch (error) {
      console.error('Error starting session in cloud:', error);
      throw error; // Re-throw to let caller handle it
    }
  },

  logSet: async (sessionId, setIn) => {
    // Cloud-only: This would need to be implemented with cloud API
    console.warn('logSet: Cloud API not implemented yet');
    return;
  },

  getLastSetsFor: async (exerciseId) => {
    // Cloud-only: This would need to be implemented with cloud API
    console.warn('getLastSetsFor: Cloud API not implemented yet');
    return [];
  },

  getBestEst1RMFor: async (exerciseId) => {
    // Cloud-only: This would need to be implemented with cloud API
    console.warn('getBestEst1RMFor: Cloud API not implemented yet');
    return null;
  },

  importPlan: (text) => { const { plan, warnings } = parsePlanText(text); set({ plan }); return { warnings }; },

  listMacroEntriesByDate: async (isoDate: string) => {
    try {
      // Cloud API only - no local fallback
      const response = await fetch(`/api/macros?date=${isoDate}`, {
        cache: 'no-store',
        headers: { 'Cache-Control': 'no-cache' }
      });
      if (response.ok) {
        const entries = await response.json();
        console.log(`🌐 Fetched ${entries.length} entries from cloud for ${isoDate}`);
        return Array.isArray(entries) ? entries : [];
      } else {
        console.error('Cloud API failed with status:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching macro entries from cloud:', error);
      return [];
    }
  },

  addMacroEntry: async (entry) => { 
    try {
      // Cloud API only - no local fallback
      const response = await fetch('/api/macros', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(entry)
      });
      
      if (response.ok) {
        console.log('✅ Macro entry saved to cloud database');
        return;
      } else {
        console.error('Failed to save macro entry, status:', response.status);
        throw new Error(`API failed with status ${response.status}`);
      }
    } catch (error) {
      console.error('Error saving macro entry to cloud:', error);
      throw error; // Re-throw to let caller handle it
    }
  },

  setMacroPlan: (mp) => set({ macroPlan: MacroPlanSchema.parse(mp) }),
  setCfg: (cfg) => set({ cfg: ProgressionConfigSchema.parse(cfg) }),
}));

function cryptoRandomId(): string { if (typeof window!=='undefined' && (window as any).crypto?.randomUUID) return (window as any).crypto.randomUUID(); return 'id-'+Math.random().toString(36).slice(2); }
