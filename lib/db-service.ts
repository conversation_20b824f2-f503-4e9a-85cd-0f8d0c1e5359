'use client';
import { SessionLog, MacroEntry, Plan, MacroPlan, ProgressionConfig } from './types';

// Client-side service for database operations
export class DatabaseService {
  private baseUrl = '/api';

  // Session Logs
  async addSessionLog(sessionLog: Omit<SessionLog, 'id'>): Promise<string> {
    const response = await fetch(`${this.baseUrl}/sessions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(sessionLog),
    });
    const { id } = await response.json();
    return id;
  }

  async updateSessionLog(sessionId: string, sessionLog: Partial<SessionLog>): Promise<void> {
    await fetch(`${this.baseUrl}/sessions/${sessionId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(sessionLog),
    });
  }

  async getSessionLog(sessionId: string): Promise<SessionLog | null> {
    const response = await fetch(`${this.baseUrl}/sessions/${sessionId}`);
    if (!response.ok) return null;
    return response.json();
  }

  async getAllSessionLogs(): Promise<SessionLog[]> {
    const response = await fetch(`${this.baseUrl}/sessions`);
    return response.json();
  }

  // Macro Entries
  async addMacroEntry(entry: Omit<MacroEntry, 'id'>): Promise<void> {
    await fetch(`${this.baseUrl}/macros`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(entry),
    });
  }

  async getMacroEntriesByDate(dateISO: string): Promise<MacroEntry[]> {
    const response = await fetch(`${this.baseUrl}/macros?date=${dateISO}`);
    return response.json();
  }

  // Plans
  async savePlan(plan: Plan): Promise<void> {
    await fetch(`${this.baseUrl}/plans`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(plan),
    });
  }

  async getActivePlan(): Promise<Plan | null> {
    const response = await fetch(`${this.baseUrl}/plans/active`);
    if (!response.ok) return null;
    return response.json();
  }

  // Macro Plans
  async saveMacroPlan(macroPlan: MacroPlan): Promise<void> {
    await fetch(`${this.baseUrl}/macro-plans`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(macroPlan),
    });
  }

  async getActiveMacroPlan(): Promise<MacroPlan | null> {
    const response = await fetch(`${this.baseUrl}/macro-plans/active`);
    if (!response.ok) return null;
    return response.json();
  }

  // Progression Config
  async saveProgressionConfig(config: ProgressionConfig): Promise<void> {
    await fetch(`${this.baseUrl}/progression-config`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config),
    });
  }

  async getProgressionConfig(): Promise<ProgressionConfig | null> {
    const response = await fetch(`${this.baseUrl}/progression-config`);
    if (!response.ok) return null;
    return response.json();
  }
}

export const dbService = new DatabaseService();
