import { createClient } from '@supabase/supabase-js';
import type { Day, MacroEntry, MacroPlan, Plan, ProgressionConfig, SessionLog } from './types';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Database service using Supabase REST API
export const supabaseDbService = {
  // Users
  async createUser(email: string) {
    const { data, error } = await supabase
      .from('users')
      .insert({ 
        id: crypto.randomUUID(),
        email
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async getUserByEmail(email: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
    return data;
  },

  // Session Logs
  async addSessionLog(log: SessionLog & { userId: string }) {
    const { data, error } = await supabase
      .from('session_logs')
      .insert({
        id: log.id,
        userId: log.userId,
        dateISO: log.dateISO,
        day: log.day,
        items: log.items,
        notes: log.notes || null,
        perceivedRecovery: log.perceivedRecovery || null
      })
      .select()
      .single();
    
    if (error) throw error;
    return data.id;
  },

  async getSessionLog(sessionId: string, userId: string) {
    const { data, error } = await supabase
      .from('session_logs')
      .select('*')
      .eq('id', sessionId)
      .eq('userId', userId)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  async updateSessionLog(sessionId: string, userId: string, updates: Partial<SessionLog>) {
    const { data, error } = await supabase
      .from('session_logs')
      .update(updates)
      .eq('id', sessionId)
      .eq('userId', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Macro Entries
  async addMacroEntry(entry: MacroEntry & { userId: string }) {
    const { data, error } = await supabase
      .from('macro_entries')
      .insert({
        id: entry.id,
        userId: entry.userId,
        dateISO: entry.dateISO,
        time: entry.time,
        protein_g: entry.protein_g,
        carbs_g: entry.carbs_g,
        fats_g: entry.fats_g,
        tag: entry.tag
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async getMacroEntriesByDate(dateISO: string, userId: string): Promise<MacroEntry[]> {
    const { data, error } = await supabase
      .from('macro_entries')
      .select('*')
      .eq('dateISO', dateISO)
      .eq('userId', userId)
      .order('time', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  // Plans
  async createPlan(plan: Plan & { userId: string; isActive?: boolean }) {
    const { data, error } = await supabase
      .from('plans')
      .insert({
        id: plan.id,
        userId: plan.userId,
        name: plan.name,
        split: plan.split,
        microcycle: plan.microcycle,
        deloadWeeks: plan.deloadWeeks,
        recovery: plan.recovery,
        isActive: plan.isActive || false
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async getActivePlan(userId: string) {
    const { data, error } = await supabase
      .from('plans')
      .select('*')
      .eq('userId', userId)
      .eq('isActive', true)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  // Macro Plans
  async createMacroPlan(plan: MacroPlan & { userId: string; isActive?: boolean }) {
    const { data, error } = await supabase
      .from('macro_plans')
      .insert({
        id: plan.id,
        userId: plan.userId,
        protein_g: plan.protein_g,
        carbs_g: plan.carbs_g,
        fats_g: plan.fats_g,
        trainingDayVariant: plan.trainingDayVariant,
        timing: plan.timing,
        isActive: plan.isActive || false
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async getActiveMacroPlan(userId: string) {
    const { data, error } = await supabase
      .from('macro_plans')
      .select('*')
      .eq('userId', userId)
      .eq('isActive', true)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  // Progression Config
  async createProgressionConfig(config: ProgressionConfig & { userId: string; id?: string }) {
    const { data, error } = await supabase
      .from('progression_configs')
      .insert({
        id: config.id || crypto.randomUUID(),
        userId: config.userId,
        upperIncKg: config.upperIncKg,
        lowerIncKg: config.lowerIncKg,
        tmPercent: config.tmPercent,
        deloadLoadPct: config.deloadLoadPct,
        deloadVolumePct: config.deloadVolumePct,
        dbIncrementKg: config.dbIncrementKg
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async getProgressionConfig(userId: string) {
    const { data, error } = await supabase
      .from('progression_configs')
      .select('*')
      .eq('userId', userId)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }
};
