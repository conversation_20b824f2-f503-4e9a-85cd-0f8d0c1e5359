import type { Exercise } from './types';

export const EXERCISES: Exercise[] = [
  { id:'machine-chest-press', name:'Machine Chest Press (neutral)', modality:'machine', muscleGroups:['chest','triceps','front-delt'], cues:['Neutral handles','Scapula retracted','Avoid wrist extension'], subs:[{id:'smith-low-incline',name:'Smith Low-Incline Press'},{id:'cable-press',name:'Cable Press (dual handles)'},{id:'db-neutral-bench',name:'DB Neutral Bench (15–20°)'}] },
  { id:'chest-supported-row', name:'Chest-Supported Row', modality:'machine', muscleGroups:['upper-back','lats'], cues:['Neutral grip','Elbows 30–45°','Chest on pad'], subs:[{id:'seated-cable-row-neutral',name:'Seated Cable Row (neutral)'},{id:'one-arm-cable-row',name:'One-Arm Cable Row'}] },
  { id:'cable-fly', name:'Cable Fly (high→low)', modality:'machine', muscleGroups:['chest'], cues:['Soft elbows','Squeeze 1s'], subs:[{id:'pec-deck',name:'Pec-deck'}] },
  { id:'machine-shoulder-press', name:'Machine Shoulder Press (neutral)', modality:'machine', muscleGroups:['shoulders','triceps'], cues:['Neutral handles','Stack wrist'], subs:[{id:'db-shoulder-press',name:'DB Neutral Shoulder Press'}] },
  { id:'lat-pulldown-neutral', name:'Lat Pulldown (neutral/underhand)', modality:'machine', muscleGroups:['lats','biceps'], cues:['Sternum up','Elbows to ribs'], subs:[{id:'pullup-assisted',name:'Assisted Neutral Pull-up'}] },
  { id:'cable-lateral-raise', name:'Cable Lateral Raise', modality:'machine', muscleGroups:['lateral-delt'], cues:['Slight lean','Lead with elbows'], subs:[{id:'machine-lateral-raise',name:'Machine Lateral Raise'}] },
  { id:'rope-pressdown', name:'Rope Pressdown', modality:'machine', muscleGroups:['triceps'], cues:['Elbows pinned','Spread rope'], subs:[{id:'ez-cable-pressdown',name:'EZ Cable Pressdown'}] },
  { id:'cable-hammer-curl', name:'Cable Hammer Curl', modality:'machine', muscleGroups:['biceps','brachialis'], cues:['Thumbs up','No sway'], subs:[{id:'db-hammer-curl',name:'DB Hammer Curl'}] },

  { id:'leg-press-45', name:'45° Leg Press', modality:'machine', muscleGroups:['quads','glutes'], cues:['Feet shoulder-width','Controlled depth'], subs:[{id:'hack-squat',name:'Hack Squat'},{id:'pendulum-squat',name:'Pendulum Squat'}] },
  { id:'smith-squat-heelwedge', name:'Smith Squat (heel wedge)', modality:'smith', muscleGroups:['quads','glutes'], cues:['Feet slightly forward','Knees track over toes'], subs:[{id:'hack-squat',name:'Hack Squat'}] },
  { id:'leg-extension', name:'Leg Extension', modality:'machine', muscleGroups:['quads'], cues:['Peak squeeze 1s'], subs:[{id:'single-leg-extension',name:'Single-leg Extension'}] },
  { id:'db-rdl', name:'DB RDL', modality:'db', muscleGroups:['hamstrings','glutes'], cues:['Soft knees','Hips back','Flat back'], subs:[{id:'smith-rdl',name:'Smith RDL'},{id:'seated-leg-curl',name:'Seated Leg Curl'}] },
  { id:'standing-calf-raise', name:'Standing Calf Raise', modality:'machine', muscleGroups:['calves'], cues:['Full stretch','Pause top'], subs:[{id:'seated-calf-raise',name:'Seated Calf Raise'}] },
  { id:'core-pallof', name:'Cable Pallof Press', modality:'machine', muscleGroups:['core'], cues:['Ribs down','No rotation'], subs:[{id:'side-plank',name:'Side Plank'}] },

  { id:'pulldown-neutral', name:'Pulldown (neutral)', modality:'machine', muscleGroups:['lats'], cues:['Shoulders down','Elbows to ribs'], subs:[{id:'pullup-assisted',name:'Assisted Pull-up'}] },
  { id:'seated-cable-row-neutral', name:'Seated Cable Row (neutral)', modality:'machine', muscleGroups:['back'], cues:['Don’t shrug','Squeeze mid-back'], subs:[{id:'chest-supported-row',name:'Chest-Supported Row'}] },
  { id:'db-low-incline-press-neutral', name:'DB Low-Incline Press (neutral)', modality:'db', muscleGroups:['chest','triceps'], cues:['Neutral grip','Wrist stacked'], subs:[{id:'cable-press',name:'Cable Press'}] },
  { id:'reverse-pec-deck', name:'Reverse Pec-deck', modality:'machine', muscleGroups:['rear-delt'], cues:['Elbows high','No lumbar arch'], subs:[{id:'cable-rear-delt-fly',name:'Cable Reverse Fly'}] },
  { id:'overhead-rope-triceps', name:'Overhead Rope Triceps', modality:'machine', muscleGroups:['triceps'], cues:['Elbows in','Stretch overhead'], subs:[{id:'single-arm-cable-triceps',name:'Single-arm Cable Ext.'}] },
  { id:'rope-hammer-curl', name:'Rope Hammer Curl', modality:'machine', muscleGroups:['biceps'], cues:['Elbows fixed'], subs:[{id:'cable-hammer-curl',name:'Cable Hammer Curl'}] },

  { id:'smith-hip-thrust', name:'Smith Hip Thrust', modality:'smith', muscleGroups:['glutes'], cues:['Pad hips','Chin tucked','Lockout'], subs:[{id:'glute-bridge',name:'Glute Bridge'}] },
  { id:'seated-leg-curl', name:'Seated Leg Curl', modality:'machine', muscleGroups:['hamstrings'], cues:['Control eccentric','Squeeze'], subs:[{id:'lying-leg-curl',name:'Lying Leg Curl'}] },
  { id:'bulgarian-split-squat', name:'Bulgarian Split Squat (DBs)', modality:'db', muscleGroups:['quads','glutes'], cues:['Torso tall','Front foot flat'], subs:[{id:'single-leg-press',name:'Single-leg Leg Press'}] },
  { id:'back-extension-glute', name:'Back Extension (glute-biased)', modality:'machine', muscleGroups:['glutes','low-back'], cues:['Round upper back','Squeeze glutes'], subs:[{id:'hip-hinge-cable',name:'Cable Hip Hinge'}] },
  { id:'seated-calf-raise', name:'Seated Calf Raise', modality:'machine', muscleGroups:['calves'], cues:['Stretch bottom','Pause top'], subs:[{id:'leg-press-calf',name:'Leg Press Calf'}] },
  { id:'core-dead-bug', name:'Dead Bug (cable assist)', modality:'machine', muscleGroups:['core'], cues:['Ribs down','Slow'], subs:[{id:'bodyweight-dead-bug',name:'BW Dead Bug'}] }
];
