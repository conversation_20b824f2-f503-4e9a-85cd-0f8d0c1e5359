import { Plan } from './types';

export const defaultPlan: Plan = {
  id: 'default-ULUL-v2',
  name: 'Upper/Lower x2 (Wrist-safe)',
  split: 'Mon Lower A • Tue Upper A • Thu Lower B • Sat Upper B',
  deloadWeeks: [8,16],
  microcycle: [
    // === MONDAY — LOWER A ===
    {
      day: 'Mon',
      mobilityOptional: false,
      warmup: {
        cardio: { mode: 'bike', minutes: 5, cue: '80–90 rpm, easy' },
        dynamic: [
          { name: 'Leg swings (front/back & side)', sets: 2, reps: '10/side' },
          { name: 'Hip circles', sets: 2, reps: '8/side' }
        ],
        ramps: [
          { exerciseId: 'leg-press-45', percents: [0.3, 0.5, 0.7, 0.8] },
          { exerciseId: 'smith-squat-heelwedge', percents: [0.4, 0.6, 0.75] }
        ]
      },
      mobility: {
        optional: true,
        items: [
          { name: 'Couch stretch', seconds: 45, side: true },
          { name: '90/90 hip flow', seconds: 30, side: true }
        ]
      },
      finisher: { type: 'z2', minutes: 10, cue: 'Incline walk or bike easy' },
      blocks: [
        {
          title: 'Lower A – Quad-biased',
          prescriptions: [
            { exerciseId: 'leg-press-45', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '3-0-1-0', restSec: 150 },
            { exerciseId: 'smith-squat-heelwedge', sets: 3, repRange: [6,10], rirTarget: 1, tempo: '3-1-1-0', restSec: 150 },
            { exerciseId: 'leg-extension', sets: 3, repRange: [10,15], rirTarget: 2, tempo: '2-1-1-1', restSec: 90 },
            { exerciseId: 'db-rdl', sets: 3, repRange: [8,12], rirTarget: 2, tempo: '3-0-1-0', restSec: 120 },
            { exerciseId: 'standing-calf-raise', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 75 },
            { exerciseId: 'core-pallof', sets: 2, repRange: [10,12], rirTarget: 2, tempo: '—', restSec: 45 }
          ]
        }
      ]
    },

    // === TUESDAY — UPPER A ===
    {
      day: 'Tue',
      mobilityOptional: true,
      warmup: {
        cardio: { mode: 'treadmill', minutes: 3, cue: 'Brisk walk or easy jog' },
        dynamic: [
          { name: 'Scap push-ups', sets: 2, reps: '8' },
          { name: 'Cable external rotations (light)', sets: 2, reps: '10/side' },
          { name: 'Wrist isometric flex/ext (neutral)', sets: 2, seconds: 20 }
        ],
        ramps: [
          { exerciseId: 'machine-chest-press', percents: [0.3, 0.5, 0.7, 0.8] },
          { exerciseId: 'chest-supported-row', percents: [0.4, 0.6, 0.8] }
        ]
      },
      mobility: {
        optional: true,
        items: [
          { name: 'Doorway pec stretch (low elbow)', seconds: 30, side: true },
          { name: 'T-spine extension over roller', seconds: 45 },
          { name: 'Wrist ulnar/radial deviation (light plate)', seconds: 20, side: true }
        ]
      },
      finisher: { type: 'walk', minutes: 8, cue: 'Incline walk easy Z2' },
      blocks: [
        {
          title: 'Upper A – Horizontal push/pull',
          prescriptions: [
            { exerciseId: 'machine-chest-press', sets: 3, repRange: [6,10], rirTarget: 1, tempo: '2-0-1-0', restSec: 150 },
            { exerciseId: 'chest-supported-row', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 120 },
            { exerciseId: 'cable-fly', sets: 3, repRange: [10,15], rirTarget: 2, tempo: '2-1-1-1', restSec: 90 },
            { exerciseId: 'machine-shoulder-press', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-0-1-0', restSec: 120 },
            { exerciseId: 'lat-pulldown-neutral', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 120 },
            { exerciseId: 'cable-lateral-raise', sets: 3, repRange: [12,20], rirTarget: 1, tempo: '2-1-1-1', restSec: 75 },
            { exerciseId: 'rope-pressdown', sets: 2, repRange: [10,15], rirTarget: 1, tempo: '2-0-1-0', restSec: 75 },
            { exerciseId: 'cable-hammer-curl', sets: 2, repRange: [10,15], rirTarget: 1, tempo: '2-0-1-0', restSec: 75 }
          ]
        }
      ]
    },

    // === THURSDAY — LOWER B ===
    {
      day: 'Thu',
      mobilityOptional: false,
      warmup: {
        cardio: { mode: 'bike', minutes: 5, cue: '80–90 rpm easy' },
        dynamic: [
          { name: 'Glute bridges', sets: 2, reps: '10' },
          { name: 'Hip airplanes (supported)', sets: 2, reps: '6/side' }
        ],
        ramps: [
          { exerciseId: 'smith-hip-thrust', percents: [0.3, 0.5, 0.7, 0.8] },
          { exerciseId: 'seated-leg-curl', percents: [0.4, 0.6, 0.8] }
        ]
      },
      mobility: {
        optional: true,
        items: [
          { name: 'Couch stretch', seconds: 45, side: true },
          { name: '90/90 hip flow', seconds: 30, side: true }
        ]
      },
      finisher: { type: 'sprints', minutes: 6, cue: '6×20s hard / 100s easy' },
      blocks: [
        {
          title: 'Lower B – Hip hinge & glutes',
          prescriptions: [
            { exerciseId: 'smith-hip-thrust', sets: 4, repRange: [6,10], rirTarget: 1, tempo: '2-1-1-1', restSec: 120 },
            { exerciseId: 'seated-leg-curl', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 90 },
            { exerciseId: 'bulgarian-split-squat', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-0-1-0', restSec: 120 },
            { exerciseId: 'back-extension-glute', sets: 2, repRange: [10,15], rirTarget: 1, tempo: '2-1-1-1', restSec: 90 },
            { exerciseId: 'seated-calf-raise', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 75 },
            { exerciseId: 'core-dead-bug', sets: 2, repRange: [8,10], rirTarget: 2, tempo: '—', restSec: 45 }
          ]
        }
      ]
    },

    // === SATURDAY — UPPER B ===
    {
      day: 'Sat',
      mobilityOptional: true,
      warmup: {
        cardio: { mode: 'walk', minutes: 3, cue: 'Brisk' },
        dynamic: [
          { name: 'High pulley face pulls (light)', sets: 2, reps: '12' },
          { name: 'Scap circles', sets: 2, reps: '8' }
        ],
        ramps: [
          { exerciseId: 'pulldown-neutral', percents: [0.4, 0.6, 0.8] },
          { exerciseId: 'machine-shoulder-press', percents: [0.3, 0.5, 0.7] }
        ]
      },
      mobility: {
        optional: true,
        items: [
          { name: 'Doorway pec stretch', seconds: 30, side: true },
          { name: 'Wrist pronation/supination (light DB)', seconds: 20, side: true }
        ]
      },
      finisher: { type: 'walk', minutes: 6, cue: 'Easy' },
      blocks: [
        {
          title: 'Upper B – Vertical push/pull + delts/arms',
          prescriptions: [
            { exerciseId: 'pulldown-neutral', sets: 3, repRange: [6,10], rirTarget: 1, tempo: '2-1-1-1', restSec: 150 },
            { exerciseId: 'machine-shoulder-press', sets: 3, repRange: [6,10], rirTarget: 1, tempo: '2-0-1-0', restSec: 150 },
            { exerciseId: 'seated-cable-row-neutral', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-1-1-1', restSec: 120 },
            { exerciseId: 'db-low-incline-press-neutral', sets: 3, repRange: [8,12], rirTarget: 1, tempo: '2-0-1-0', restSec: 120 },
            { exerciseId: 'reverse-pec-deck', sets: 3, repRange: [12,20], rirTarget: 1, tempo: '2-1-1-1', restSec: 75 },
            { exerciseId: 'overhead-rope-triceps', sets: 2, repRange: [10,12], rirTarget: 1, tempo: '2-0-1-0', restSec: 75 },
            { exerciseId: 'rope-hammer-curl', sets: 2, repRange: [10,12], rirTarget: 1, tempo: '2-0-1-0', restSec: 75 }
          ]
        }
      ]
    }
  ],
  recovery: [
    {
      day: 'Wed',
      title: 'Recovery & Mobility (Upper bias)',
      mobility: {
        optional: false,
        items: [
          { name: 'Doorway pec stretch (low elbow)', seconds: 30, side: true },
          { name: 'T-spine extension over roller', seconds: 45 },
          { name: 'Wrist flexor/extensor isometrics (neutral)', seconds: 20, side: true }
        ]
      },
      conditioning: { type: 'z2', minutes: 20, cue: 'Bike or incline walk RPE 3–4' }
    },
    {
      day: 'Sun',
      title: 'Recovery & Mobility (Lower bias)',
      mobility: {
        optional: false,
        items: [
          { name: 'Couch stretch', seconds: 45, side: true },
          { name: '90/90 hip flow', seconds: 30, side: true },
          { name: 'Ankle dorsiflexion rocks', seconds: 30, side: true }
        ]
      },
      conditioning: { type: 'walk', minutes: 25, cue: 'Easy outdoor walk; nose-breathing' }
    }
  ]
};
