import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "./prisma";

const isGoogleConfigured = process.env.GOOGLE_CLIENT_ID && 
  process.env.GOOGLE_CLIENT_ID !== "dummy-client-id" &&
  process.env.GOOGLE_CLIENT_SECRET && 
  process.env.GOOGLE_CLIENT_SECRET !== "dummy-client-secret";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    // Only include Google provider if properly configured
    ...(isGoogleConfigured ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID!,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      })
    ] : []),
    // Development-only provider for testing
    ...(!isGoogleConfigured ? [
      CredentialsProvider({
        id: "dev-login",
        name: "<PERSON> Login",
        credentials: {
          email: { label: "Email", type: "email", placeholder: "<EMAIL>" }
        },
        async authorize(credentials) {
          // This is for development only - creates a test user
          if (credentials?.email) {
            return {
              id: "dev-user-1",
              email: credentials.email,
              name: "Development User",
            };
          }
          return null;
        },
      })
    ] : []),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user && token?.sub) {
        session.user.id = token.sub;
      }
      return session;
    },
    jwt: async ({ user, token }) => {
      if (user) {
        token.sub = user.id;
      }
      return token;
    },
  },
};
