'use client';
import { db as dexieDb } from './db';
import { dbService } from './db-service';

export class DataMigration {
  async migrateToCloud(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      // Migrate session logs
      const sessions = await dexieDb.sessionLogs.toArray();
      for (const session of sessions) {
        try {
          await dbService.addSessionLog({
            dateISO: session.dateISO,
            day: session.day,
            items: session.items,
            notes: session.notes,
            perceivedRecovery: session.perceivedRecovery,
          });
        } catch (error) {
          errors.push(`Failed to migrate session ${session.id}: ${error}`);
        }
      }

      // Migrate macro entries
      const macroEntries = await dexieDb.macroEntries.toArray();
      for (const entry of macroEntries) {
        try {
          await dbService.addMacroEntry({
            dateISO: entry.dateISO,
            time: entry.time,
            protein_g: entry.protein_g,
            carbs_g: entry.carbs_g,
            fats_g: entry.fats_g,
            tag: entry.tag,
          });
        } catch (error) {
          errors.push(`Failed to migrate macro entry ${entry.id}: ${error}`);
        }
      }

      return { success: errors.length === 0, errors };
    } catch (error) {
      return { success: false, errors: [`Migration failed: ${error}`] };
    }
  }

  async clearLocalData(): Promise<void> {
    await dexieDb.sessionLogs.clear();
    await dexieDb.macroEntries.clear();
  }
}
