'use client';
import { create } from 'zustand';
import { db } from './db';
import { defaultPlan } from './planDefaults';
import type { Day, MacroEntry, MacroPlan, Plan, ProgressionConfig, SessionLog, SetLog } from './types';
import { MacroPlan as MacroPlanSchema, ProgressionConfig as ProgressionConfigSchema } from './types';
import { epley1RM, suggestNextLoad } from './progression';
import { parsePlanText } from './parser';

type AppState = {
  plan: Plan;
  macroPlan: MacroPlan;
  cfg: ProgressionConfig;
  today: () => { day: Day, dateISO: string };
  startSession: (day: Day) => Promise<string>;
  logSet: (sessionId: string, set: Omit<SetLog, 'est1RM'|'completedAt'>) => Promise<void>;
  getLastSetsFor: (exerciseId: string) => Promise<SetLog[]>;
  getBestEst1RMFor: (exerciseId: string) => Promise<number | null>;
  importPlan: (text: string) => { warnings: string[] };
  listMacroEntriesByDate: (isoDate: string) => Promise<MacroEntry[]>;
  addMacroEntry: (entry: Omit<MacroEntry,'id'>) => Promise<void>;
  setMacroPlan: (mp: MacroPlan) => void;
  setCfg: (cfg: ProgressionConfig) => void;
};

const initialMacroPlan: MacroPlan = MacroPlanSchema.parse({
  id: 'default',
  protein_g: 150, carbs_g: 300, fats_g: 89,
  trainingDayVariant: { carbs_g: 380 },
  timing: { preWO: { start: '08:00', end: '09:00', targetCarbs_g: 70 }, intra: { targetCarbs_g: 20 }, postWO: { windowMin: 120, targetCarbs_g: 70 } }
});
const initialCfg: ProgressionConfig = ProgressionConfigSchema.parse({ upperIncKg:2.5, lowerIncKg:5, tmPercent:0.88, deloadLoadPct:0.9, deloadVolumePct:0.65, dbIncrementKg:2.5 });

export const useStore = create<AppState>()((set, get) => ({
  plan: defaultPlan,
  macroPlan: initialMacroPlan,
  cfg: initialCfg,

  today: () => {
    const now = new Date();
    const d = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'][now.getDay()] as Day;
    const dateISO = new Date(now.getTime() - now.getTimezoneOffset()*60000).toISOString().slice(0,10);
    return { day: d, dateISO };
  },

  startSession: async (day) => {
    const id = cryptoRandomId();
    const { dateISO } = get().today();
    const log: SessionLog = { id, dateISO, day, items: [] };
    await db.sessionLogs.add(log);
    return id;
  },

  logSet: async (sessionId, setIn) => {
    const completedAt = new Date().toISOString();
    const est1RM = epley1RM(setIn.weightKg, setIn.reps);
    const setLog = { ...setIn, est1RM, completedAt };
    const sess = await db.sessionLogs.get(sessionId);
    if (!sess) return;
    sess.items.push(setLog);
    await db.sessionLogs.put(sess);
  },

  getLastSetsFor: async (exerciseId) => {
    const all = await db.sessionLogs.toArray();
    const sets: SetLog[] = [];
    for (const s of all) for (const it of s.items) if (it.exerciseId === exerciseId) sets.push(it);
    sets.sort((a,b)=>a.completedAt.localeCompare(b.completedAt));
    return sets.slice(-5);
  },

  getBestEst1RMFor: async (exerciseId) => {
    const sets = await get().getLastSetsFor(exerciseId);
    const vals = sets.map(s => s.est1RM || 0);
    return vals.length ? Math.max(...vals) : null;
  },

  importPlan: (text) => { const { plan, warnings } = parsePlanText(text); set({ plan }); return { warnings }; },

  listMacroEntriesByDate: async (isoDate) => db.macroEntries.where('dateISO').equals(isoDate).toArray(),

  addMacroEntry: async (entry) => { await db.macroEntries.add({ id: cryptoRandomId(), ...entry }); },

  setMacroPlan: (mp) => set({ macroPlan: MacroPlanSchema.parse(mp) }),
  setCfg: (cfg) => set({ cfg: ProgressionConfigSchema.parse(cfg) }),
}));

function cryptoRandomId(): string { if (typeof window!=='undefined' && (window as any).crypto?.randomUUID) return (window as any).crypto.randomUUID(); return 'id-'+Math.random().toString(36).slice(2); }
