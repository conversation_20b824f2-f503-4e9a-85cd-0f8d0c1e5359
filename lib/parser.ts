import { Plan } from './types';
import { defaultPlan } from './planDefaults';

export function cryptoRandomId(): string {
  return crypto.randomUUID();
}

// Very light parser: try JSON Plan; else fallback to defaultPlan.
export function parsePlanText(input: string): { plan: Plan, warnings: string[] } {
  const warnings: string[] = [];
  try {
    const obj = JSON.parse(input);
    // We trust shape at runtime; in UI we'd validate with zod if desired
    return { plan: obj as Plan, warnings };
  } catch {}
  warnings.push('Markdown parsing is limited in this build; using default plan.');
  return { plan: defaultPlan, warnings };
}
