import { SetLog, Prescription, ProgressionConfig } from './types';

export function epley1RM(weightKg: number, reps: number): number {
  return Math.max(0, weightKg * (1 + reps / 30));
}
export function bestEst1RMForExercise(sets: SetLog[]): number | null {
  const vals = sets.map(s => s.est1RM ?? epley1RM(s.weightKg, s.reps));
  return vals.length ? Math.max(...vals) : null;
}
export function computeTM(bestEst1RM: number, tmPercent: number): number {
  return bestEst1RM * tmPercent;
}
function roundToIncrement(value: number, step: number): number {
  return Math.round(value / step) * step;
}

export type LastSessionOutcome = {
  hitTopRangeAllSetsAtRIR2OrLess: boolean;
  midRangeRIR1to3: boolean;
  belowRangeOrHighRIR: boolean;
};
export function summarizeOutcome(p: Prescription, sets: SetLog[]): LastSessionOutcome {
  const [minR, maxR] = p.repRange;
  const lastSet = sets[sets.length - 1];
  const lastRIR = lastSet?.rir ?? 3;
  const repsAll = sets.map(s => s.reps);
  const allAtTop = repsAll.every(r => r >= maxR);
  const allInMid = repsAll.every(r => r >= Math.round((minR+maxR)/2) && r <= maxR);
  const anyBelowMin = repsAll.some(r => r < minR);
  return {
    hitTopRangeAllSetsAtRIR2OrLess: allAtTop && lastRIR <= 2,
    midRangeRIR1to3: allInMid && lastRIR >= 1 && lastRIR <= 3,
    belowRangeOrHighRIR: anyBelowMin || lastRIR > 3
  };
}

export function suggestNextLoad(args: {
  prescription: Prescription;
  lastSets?: SetLog[];
  lastWeightKg?: number;
  bestEst1RM?: number | null;
  cfg: ProgressionConfig;
  isUpper: boolean;
}): number | null {
  const { prescription: p, lastSets, lastWeightKg, bestEst1RM, cfg, isUpper } = args;
  const inc = isUpper ? cfg.upperIncKg : cfg.lowerIncKg;
  const [minR, maxR] = p.repRange;

  let base: number | null = null;
  if (bestEst1RM && bestEst1RM > 0) {
    const TM = computeTM(bestEst1RM, cfg.tmPercent);
    const targetReps = Math.max(minR, maxR + 2);
    const w = TM / (1 + targetReps / 30);
    base = Math.max(0, w);
  }
  if (lastSets && lastSets.length > 0 && typeof lastWeightKg === 'number') {
    const outcome = summarizeOutcome(p, lastSets);
    const step = p.exerciseId.toLowerCase().includes('db') ? 2.5 : 2.5;
    if (outcome.hitTopRangeAllSetsAtRIR2OrLess) return roundToIncrement(lastWeightKg + inc, step);
    if (outcome.midRangeRIR1to3) return lastWeightKg;
    if (outcome.belowRangeOrHighRIR) return Math.max(step, roundToIncrement(lastWeightKg * 0.95, step));
  }
  if (base) {
    const step = p.exerciseId.toLowerCase().includes('db') ? 2.5 : 2.5;
    return Math.max(step, roundToIncrement(base, step));
  }
  return null;
}
