import { z } from 'zod';

export const Days = z.enum(['Mon','Tue','Wed','Thu','Sat','Sun']);
export type Day = z.infer<typeof Days>;

export const UserProfile = z.object({
  id: z.string(),
  tz: z.string().default('Africa/Casablanca'),
  units: z.literal('kg').default('kg'),
  schedule: z.object({
    days: z.array(Days).default(['Mon','Tue','Thu','Sat']),
    trainWindow: z.string().default('09:00-10:30')
  }),
  wristNotes: z.string().optional()
});
export type UserProfile = z.infer<typeof UserProfile>;

export const ExerciseRef = z.object({ id: z.string(), name: z.string() });
export type ExerciseRef = z.infer<typeof ExerciseRef>;

export const Exercise = z.object({
  id: z.string(),
  name: z.string(),
  modality: z.enum(['machine','smith','db']),
  muscleGroups: z.array(z.string()),
  cues: z.array(z.string()).default([]),
  subs: z.array(ExerciseRef).default([])
});
export type Exercise = z.infer<typeof Exercise>;

export const Warmup = z.object({
  cardio: z.object({ mode: z.enum(['treadmill','bike','stairs','walk']), minutes: z.number().positive(), cue: z.string().optional() }).optional(),
  dynamic: z.array(z.object({ name: z.string(), sets: z.number().int().positive().optional(), reps: z.string().optional(), seconds: z.number().int().positive().optional() })).optional(),
  ramps: z.array(z.object({ exerciseId: z.string(), percents: z.array(z.number()).default([0.3,0.5,0.7,0.8])})).optional()
});
export type Warmup = z.infer<typeof Warmup>;

export const Mobility = z.object({
  items: z.array(z.object({ name: z.string(), seconds: z.number().int().positive(), side: z.boolean().default(false).optional() })),
  optional: z.boolean().default(true)
});
export type Mobility = z.infer<typeof Mobility>;

export const Finisher = z.object({ type: z.enum(['z2','sprints','walk']), minutes: z.number().int().positive(), cue: z.string().optional() });
export type Finisher = z.infer<typeof Finisher>;

export const Prescription = z.object({
  exerciseId: z.string(),
  sets: z.number().int().positive(),
  repRange: z.tuple([z.number().int().positive(), z.number().int().positive()]),
  rirTarget: z.number().int().min(0).max(5).optional(),
  tempo: z.string().optional(),
  restSec: z.number().int().positive().optional()
});
export type Prescription = z.infer<typeof Prescription>;

export const Block = z.object({ title: z.string().optional(), prescriptions: z.array(Prescription) });
export type Block = z.infer<typeof Block>;

export const DayPlan = z.object({
  day: Days,
  blocks: z.array(Block),
  mobilityOptional: z.boolean().default(false),
  warmup: Warmup.optional(),
  mobility: Mobility.optional(),
  finisher: Finisher.optional()
});
export type DayPlan = z.infer<typeof DayPlan>;

export const Plan = z.object({
  id: z.string(), name: z.string(), split: z.string(),
  microcycle: z.array(DayPlan),
  deloadWeeks: z.array(z.number().int().positive()).optional(),
  recovery: z.array(z.object({
    day: z.enum(['Wed','Sun']),
    title: z.string().default('Recovery & Mobility'),
    mobility: Mobility,
    conditioning: Finisher.optional()
  })).optional()
});
export type Plan = z.infer<typeof Plan>;

export const SetLog = z.object({ exerciseId: z.string(), setIndex: z.number().int().nonnegative(), weightKg: z.number().nonnegative(), reps: z.number().int().nonnegative(), rir: z.number().int().min(0).max(10).optional(), est1RM: z.number().nonnegative().optional(), completedAt: z.string() });
export type SetLog = z.infer<typeof SetLog>;

export const SessionLog = z.object({ id: z.string(), dateISO: z.string(), day: Days, items: z.array(SetLog), notes: z.string().optional(), perceivedRecovery: z.number().int().min(1).max(10).optional() });
export type SessionLog = z.infer<typeof SessionLog>;

export const MacroPlan = z.object({
  id: z.string(),
  protein_g: z.number().nonnegative(),
  carbs_g: z.number().nonnegative(),
  fats_g: z.number().nonnegative(),
  trainingDayVariant: z.object({ carbs_g: z.number().nonnegative() }).optional(),
  timing: z.object({
    preWO: z.object({ start: z.string(), end: z.string(), targetCarbs_g: z.number().nonnegative() }),
    intra: z.object({ targetCarbs_g: z.number().nonnegative() }).optional(),
    postWO: z.object({ windowMin: z.number().int().positive(), targetCarbs_g: z.number().nonnegative() })
  })
});
export type MacroPlan = z.infer<typeof MacroPlan>;

export const MacroEntry = z.object({ id: z.string(), dateISO: z.string(), time: z.string(), protein_g: z.number().nonnegative(), carbs_g: z.number().nonnegative(), fats_g: z.number().nonnegative(), tag: z.enum(['preWO','intra','postWO','meal']).default('meal') });
export type MacroEntry = z.infer<typeof MacroEntry>;

export const ProgressionConfig = z.object({ upperIncKg: z.number().positive().default(2.5), lowerIncKg: z.number().positive().default(5), tmPercent: z.number().positive().max(1).default(0.88), deloadLoadPct: z.number().positive().max(1).default(0.9), deloadVolumePct: z.number().positive().max(1).default(0.65), dbIncrementKg: z.number().positive().default(2.5) });
export type ProgressionConfig = z.infer<typeof ProgressionConfig>;
