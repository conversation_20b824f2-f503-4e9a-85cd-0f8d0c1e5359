-- Create users table
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- Create unique index on email
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- Create plans table
CREATE TABLE "plans" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "split" TEXT NOT NULL,
    "microcycle" JSONB NOT NULL,
    "deloadWeeks" INTEGER[],
    "recovery" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONS<PERSON><PERSON>INT "plans_pkey" PRIMARY KEY ("id")
);

-- <PERSON><PERSON> session_logs table
CREATE TABLE "session_logs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "dateISO" TEXT NOT NULL,
    "day" TEXT NOT NULL,
    "items" JSONB NOT NULL,
    "notes" TEXT,
    "perceivedRecovery" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "session_logs_pkey" PRIMARY KEY ("id")
);

-- Create macro_entries table
CREATE TABLE "macro_entries" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "dateISO" TEXT NOT NULL,
    "time" TEXT NOT NULL,
    "protein_g" DOUBLE PRECISION NOT NULL,
    "carbs_g" DOUBLE PRECISION NOT NULL,
    "fats_g" DOUBLE PRECISION NOT NULL,
    "tag" TEXT NOT NULL DEFAULT 'meal',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "macro_entries_pkey" PRIMARY KEY ("id")
);

-- Create macro_plans table
CREATE TABLE "macro_plans" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "protein_g" DOUBLE PRECISION NOT NULL,
    "carbs_g" DOUBLE PRECISION NOT NULL,
    "fats_g" DOUBLE PRECISION NOT NULL,
    "trainingDayVariant" JSONB,
    "timing" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "macro_plans_pkey" PRIMARY KEY ("id")
);

-- Create progression_configs table
CREATE TABLE "progression_configs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "upperIncKg" DOUBLE PRECISION NOT NULL DEFAULT 2.5,
    "lowerIncKg" DOUBLE PRECISION NOT NULL DEFAULT 5.0,
    "tmPercent" DOUBLE PRECISION NOT NULL DEFAULT 0.88,
    "deloadLoadPct" DOUBLE PRECISION NOT NULL DEFAULT 0.9,
    "deloadVolumePct" DOUBLE PRECISION NOT NULL DEFAULT 0.65,
    "dbIncrementKg" DOUBLE PRECISION NOT NULL DEFAULT 2.5,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "progression_configs_pkey" PRIMARY KEY ("id")
);

-- Create unique index on userId for progression_configs
CREATE UNIQUE INDEX "progression_configs_userId_key" ON "progression_configs"("userId");

-- Add foreign key constraints
ALTER TABLE "plans" ADD CONSTRAINT "plans_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "session_logs" ADD CONSTRAINT "session_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "macro_entries" ADD CONSTRAINT "macro_entries_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "macro_plans" ADD CONSTRAINT "macro_plans_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "progression_configs" ADD CONSTRAINT "progression_configs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
