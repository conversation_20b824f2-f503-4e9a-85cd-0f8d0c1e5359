# GYM Coach — PWA (V2)

Mobile-first **Next.js 14 + TS + Tai<PERSON><PERSON> + <PERSON>ustand + <PERSON>ie**.  
**Offline-first**. Includes **warm-ups**, **mobility**, **ramp-up sets**, **finishers**, **rest-day recovery pages**, and **both per-set and between-exercise timers**.

## Dev
```bash
npm i
npm run dev
```
Install as <PERSON><PERSON> from the browser menu.

## Build
```bash
npm run build
npm start
```

## Features
- Today view shows **today’s session** or **rest-day recovery**, plus macro progress rings and carb timing.
- Session Mode shows **warm-up (cardio+dynamic + ramp sets)**, optional **mobility**, **main work** with set logger + **rest timer**, and **finisher**.
- **Between-exercise timer** at session header.
- Macro tracker with training/rest targets, pre/intra/post timing hints.
- Plan import (JSON) and preview.
- Offline via Service Worker; data in IndexedDB (Dexie).
- Export logs as JSON.

## Data model
See `lib/types.ts`: `Plan/DayPlan/Warmup/Mobility/Finisher/Recovery`, `SessionLog/SetLog`, `MacroPlan/MacroEntry`, `ProgressionConfig`.

## Auto-progression
Epley -> TM (0.88) -> Double-progression rules:
- Top-of-range at ≤2 RIR → increase (+2.5 kg upper, +5 kg lower if you choose).
- Mid-range with RIR 1–3 → hold.
- Below range or RIR >3 → −5%.
Ramp-up sets are computed from the **suggested work weight** with % sequences per exercise.

## Deploy
Vercel/Netlify. PWA manifest at `public/manifest.webmanifest`; SW in `public/sw.js`.
