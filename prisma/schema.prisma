// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sessionLogs       SessionLog[]
  macroEntries      MacroEntry[]
  plans             Plan[]
  macroPlans        MacroPlan[]
  progressionConfig ProgressionConfig?

  @@map("users")
}

model Plan {
  id           String @id
  userId       String
  name         String
  split        String
  microcycle   Json // Store the microcycle as JSON
  deloadWeeks  Int[]
  recovery     Json? // Store recovery as JSON
  isActive     Boolean @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("plans")
}

model SessionLog {
  id                 String   @id
  userId             String
  dateISO            String
  day                String
  items              Json // Store SetLog array as JSON
  notes              String?
  perceivedRecovery  Int?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session_logs")
}

model MacroEntry {
  id        String   @id
  userId    String
  dateISO   String
  time      String
  protein_g Float
  carbs_g   Float
  fats_g    Float
  tag       String   @default("meal") // preWO, intra, postWO, meal
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("macro_entries")
}

model MacroPlan {
  id                  String @id
  userId              String
  protein_g           Float
  carbs_g             Float
  fats_g              Float
  trainingDayVariant  Json? // Store training day variant as JSON
  timing              Json  // Store timing configuration as JSON
  isActive            Boolean @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("macro_plans")
}

model ProgressionConfig {
  id              String  @id @default(cuid())
  userId          String  @unique
  upperIncKg      Float   @default(2.5)
  lowerIncKg      Float   @default(5.0)
  tmPercent       Float   @default(0.88)
  deloadLoadPct   Float   @default(0.9)
  deloadVolumePct Float   @default(0.65)
  dbIncrementKg   Float   @default(2.5)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("progression_configs")
}
